import 'package:flutter/foundation.dart';
import '../../data/models/volunteer_profile_model.dart';
import '../../data/models/microtask_model.dart';
import '../../data/models/user_microtask_model.dart';
import '../../data/repositories/task_repository.dart';
import '../../data/repositories/event_repository.dart';
import '../../core/constants/enums.dart';
import '../../core/exceptions/app_exceptions.dart';

/// Estados possíveis do controller de voluntários
enum VolunteerControllerState { initial, loading, loaded, error }

/// Controller responsável pelo gerenciamento de voluntários e suas atribuições
/// Complementa o TaskController focando especificamente na gestão de voluntários
class VolunteerController extends ChangeNotifier {
  final TaskRepository _taskRepository = TaskRepository();
  final EventRepository _eventRepository = EventRepository();

  // Estado do controller
  VolunteerControllerState _state = VolunteerControllerState.initial;
  String? _errorMessage;

  // Dados dos voluntários
  List<VolunteerProfileModel> _eventVolunteers = [];
  List<VolunteerProfileModel> _availableVolunteers = [];
  List<VolunteerProfileModel> _compatibleVolunteers = [];
  List<VolunteerProfileModel> _assignedVolunteers = [];

  // Dados de atribuições
  List<UserMicrotaskModel> _userMicrotasks = [];
  Map<String, List<MicrotaskModel>> _volunteerMicrotasks = {};
  Map<String, List<UserMicrotaskModel>> _volunteerAssignments = {};

  // Getters
  VolunteerControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<VolunteerProfileModel> get eventVolunteers => _eventVolunteers;
  List<VolunteerProfileModel> get availableVolunteers => _availableVolunteers;
  List<VolunteerProfileModel> get compatibleVolunteers => _compatibleVolunteers;
  List<VolunteerProfileModel> get assignedVolunteers => _assignedVolunteers;
  List<UserMicrotaskModel> get userMicrotasks => _userMicrotasks;
  Map<String, List<MicrotaskModel>> get volunteerMicrotasks => _volunteerMicrotasks;
  Map<String, List<UserMicrotaskModel>> get volunteerAssignments => _volunteerAssignments;

  // Getters de conveniência
  bool get isLoading => _state == VolunteerControllerState.loading;
  bool get hasError => _state == VolunteerControllerState.error;
  bool get isLoaded => _state == VolunteerControllerState.loaded;
  bool get isEmpty => _eventVolunteers.isEmpty;

  /// Carrega todos os voluntários de um evento
  Future<void> loadEventVolunteers(String eventId) async {
    try {
      _setState(VolunteerControllerState.loading);
      _eventVolunteers = await _eventRepository.getEventVolunteers(eventId);
      _setState(VolunteerControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar voluntários: ${e.toString()}');
    }
  }

  /// Carrega voluntários disponíveis (não atribuídos a uma microtask específica)
  Future<void> loadAvailableVolunteers(String eventId, String microtaskId) async {
    try {
      _setState(VolunteerControllerState.loading);
      
      final allVolunteers = await _eventRepository.getEventVolunteers(eventId);
      final microtask = await _taskRepository.getMicrotaskById(microtaskId);
      
      if (microtask != null) {
        _availableVolunteers = allVolunteers
            .where((volunteer) => !microtask.isUserAssigned(volunteer.userId))
            .toList();
      } else {
        _availableVolunteers = allVolunteers;
      }
      
      _setState(VolunteerControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar voluntários disponíveis: ${e.toString()}');
    }
  }

  /// Carrega voluntários compatíveis com uma microtask
  Future<void> loadCompatibleVolunteers(String microtaskId) async {
    try {
      _setState(VolunteerControllerState.loading);
      _compatibleVolunteers = await _taskRepository.getCompatibleVolunteers(microtaskId);
      _setState(VolunteerControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar voluntários compatíveis: ${e.toString()}');
    }
  }

  /// Carrega voluntários atribuídos a uma microtask
  Future<void> loadAssignedVolunteers(String microtaskId) async {
    try {
      _setState(VolunteerControllerState.loading);
      _assignedVolunteers = await _taskRepository.getAssignedVolunteers(microtaskId);
      _setState(VolunteerControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar voluntários atribuídos: ${e.toString()}');
    }
  }

  /// Carrega as microtasks atribuídas a um voluntário específico
  Future<void> loadVolunteerMicrotasks(String userId, String eventId) async {
    try {
      final userMicrotasks = await _taskRepository.getUserAssignedMicrotasks(userId, eventId);
      final microtasks = <MicrotaskModel>[];
      
      for (final userMicrotask in userMicrotasks) {
        final microtask = await _taskRepository.getMicrotaskById(userMicrotask.microtaskId);
        if (microtask != null) {
          microtasks.add(microtask);
        }
      }
      
      _volunteerMicrotasks[userId] = microtasks;
      _volunteerAssignments[userId] = userMicrotasks;
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar microtasks do voluntário: ${e.toString()}');
    }
  }

  /// Carrega as atribuições de todos os voluntários de um evento
  Future<void> loadAllVolunteerAssignments(String eventId) async {
    try {
      _setState(VolunteerControllerState.loading);
      
      final volunteers = await _eventRepository.getEventVolunteers(eventId);
      
      for (final volunteer in volunteers) {
        await loadVolunteerMicrotasks(volunteer.userId, eventId);
      }
      
      _setState(VolunteerControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar atribuições: ${e.toString()}');
    }
  }

  /// Atribui um voluntário a uma microtask
  Future<bool> assignVolunteerToMicrotask(String userId, String microtaskId) async {
    try {
      _setState(VolunteerControllerState.loading);
      
      await _taskRepository.assignVolunteerToMicrotask(userId, microtaskId);
      
      // Atualiza as listas locais
      await _refreshAssignmentData(userId, microtaskId);
      
      _setState(VolunteerControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao atribuir voluntário: ${e.toString()}');
      return false;
    }
  }

  /// Remove um voluntário de uma microtask
  Future<bool> removeVolunteerFromMicrotask(String userId, String microtaskId) async {
    try {
      _setState(VolunteerControllerState.loading);
      
      await _taskRepository.removeVolunteerFromMicrotask(userId, microtaskId);
      
      // Atualiza as listas locais
      await _refreshAssignmentData(userId, microtaskId);
      
      _setState(VolunteerControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao remover voluntário: ${e.toString()}');
      return false;
    }
  }

  /// Atualiza o status de uma microtask para um voluntário
  Future<bool> updateVolunteerMicrotaskStatus(
    String microtaskId, 
    String userId, 
    UserMicrotaskStatus status
  ) async {
    try {
      _setState(VolunteerControllerState.loading);
      
      await _taskRepository.updateUserMicrotaskStatus(microtaskId, userId, status);
      
      // Atualiza as atribuições do voluntário
      final microtask = await _taskRepository.getMicrotaskById(microtaskId);
      if (microtask != null) {
        await loadVolunteerMicrotasks(userId, microtask.eventId);
      }
      
      _setState(VolunteerControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao atualizar status: ${e.toString()}');
      return false;
    }
  }

  /// Verifica se um voluntário pode ser atribuído a uma microtask
  Future<bool> canAssignVolunteer(String userId, String microtaskId) async {
    try {
      return await _taskRepository.canAssignVolunteer(userId, microtaskId);
    } catch (e) {
      return false;
    }
  }

  /// Promove um voluntário a gerenciador
  Future<bool> promoteVolunteer(String eventId, String userId, String managerId) async {
    try {
      _setState(VolunteerControllerState.loading);
      
      await _eventRepository.promoteVolunteer(eventId, userId, managerId);
      
      // Recarrega a lista de voluntários
      await loadEventVolunteers(eventId);
      
      _setState(VolunteerControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao promover voluntário: ${e.toString()}');
      return false;
    }
  }

  /// Busca um voluntário por ID
  VolunteerProfileModel? getVolunteerById(String userId) {
    try {
      return _eventVolunteers.firstWhere((volunteer) => volunteer.userId == userId);
    } catch (e) {
      return null;
    }
  }

  /// Filtra voluntários por habilidade
  List<VolunteerProfileModel> getVolunteersBySkill(String skill) {
    return _eventVolunteers
        .where((volunteer) => volunteer.skills.contains(skill))
        .toList();
  }

  /// Filtra voluntários por recurso
  List<VolunteerProfileModel> getVolunteersByResource(String resource) {
    return _eventVolunteers
        .where((volunteer) => volunteer.resources.contains(resource))
        .toList();
  }

  /// Filtra voluntários por disponibilidade em um dia específico
  List<VolunteerProfileModel> getVolunteersByDay(String day) {
    return _eventVolunteers
        .where((volunteer) => volunteer.availableDays.contains(day))
        .toList();
  }

  /// Obtém estatísticas de um voluntário
  Map<String, dynamic> getVolunteerStats(String userId) {
    final assignments = _volunteerAssignments[userId] ?? [];
    final microtasks = _volunteerMicrotasks[userId] ?? [];
    
    final totalAssignments = assignments.length;
    final completedAssignments = assignments.where((a) => a.isCompleted).length;
    final inProgressAssignments = assignments.where((a) => a.isInProgress).length;
    final totalHours = assignments.fold<int>(0, (sum, a) => sum + a.hoursWorked);
    
    return {
      'totalAssignments': totalAssignments,
      'completedAssignments': completedAssignments,
      'inProgressAssignments': inProgressAssignments,
      'completionRate': totalAssignments > 0 ? completedAssignments / totalAssignments : 0.0,
      'totalHours': totalHours,
      'averageHoursPerTask': totalAssignments > 0 ? totalHours / totalAssignments : 0.0,
    };
  }

  /// Atualiza os dados de atribuição após uma mudança
  Future<void> _refreshAssignmentData(String userId, String microtaskId) async {
    try {
      // Recarrega voluntários atribuídos se a lista estava carregada
      if (_assignedVolunteers.isNotEmpty) {
        await loadAssignedVolunteers(microtaskId);
      }
      
      // Recarrega voluntários disponíveis se a lista estava carregada
      if (_availableVolunteers.isNotEmpty) {
        final microtask = await _taskRepository.getMicrotaskById(microtaskId);
        if (microtask != null) {
          await loadAvailableVolunteers(microtask.eventId, microtaskId);
        }
      }
      
      // Recarrega as microtasks do voluntário
      final microtask = await _taskRepository.getMicrotaskById(microtaskId);
      if (microtask != null) {
        await loadVolunteerMicrotasks(userId, microtask.eventId);
      }
    } catch (e) {
      // Erro silencioso para não interromper o fluxo principal
      debugPrint('Erro ao atualizar dados de atribuição: $e');
    }
  }

  /// Limpa todos os dados
  void clearData() {
    _eventVolunteers.clear();
    _availableVolunteers.clear();
    _compatibleVolunteers.clear();
    _assignedVolunteers.clear();
    _userMicrotasks.clear();
    _volunteerMicrotasks.clear();
    _volunteerAssignments.clear();
    _setState(VolunteerControllerState.initial);
  }

  /// Limpa apenas o erro
  void clearError() {
    if (_state == VolunteerControllerState.error) {
      _errorMessage = null;
      _setState(VolunteerControllerState.initial);
    }
  }

  /// Atualiza o estado do controller
  void _setState(VolunteerControllerState newState) {
    _state = newState;
    if (newState != VolunteerControllerState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Define um erro e atualiza o estado
  void _setError(String error) {
    _errorMessage = error;
    _state = VolunteerControllerState.error;
    notifyListeners();
  }

  @override
  void dispose() {
    clearData();
    super.dispose();
  }
}
