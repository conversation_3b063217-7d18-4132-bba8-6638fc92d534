import 'package:flutter/foundation.dart';
import '../../data/models/task_model.dart';
import '../../data/models/microtask_model.dart';
import '../../data/models/user_microtask_model.dart';
import '../../data/models/volunteer_profile_model.dart';
import '../../data/repositories/task_repository.dart';
import '../../core/constants/enums.dart';
import '../../core/exceptions/app_exceptions.dart';

/// Estados possíveis do controller de tasks
enum TaskControllerState { initial, loading, loaded, error }

/// Controller responsável pelo gerenciamento de estado de Tasks e Microtasks
/// Utiliza Provider para notificar mudanças na UI
class TaskController extends ChangeNotifier {
  final TaskRepository _taskRepository = TaskRepository();

  // Estado do controller
  TaskControllerState _state = TaskControllerState.initial;
  String? _errorMessage;

  // Dados das tasks e microtasks
  List<TaskModel> _tasks = [];
  List<MicrotaskModel> _microtasks = [];
  Map<TaskModel, List<MicrotaskModel>> _tasksHierarchy = {};
  Map<String, dynamic> _eventProgress = {};

  // Dados de atribuição
  List<VolunteerProfileModel> _compatibleVolunteers = [];
  List<VolunteerProfileModel> _assignedVolunteers = [];
  List<UserMicrotaskModel> _userMicrotasks = [];

  // Getters
  TaskControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<TaskModel> get tasks => _tasks;
  List<MicrotaskModel> get microtasks => _microtasks;
  Map<TaskModel, List<MicrotaskModel>> get tasksHierarchy => _tasksHierarchy;
  Map<String, dynamic> get eventProgress => _eventProgress;
  List<VolunteerProfileModel> get compatibleVolunteers => _compatibleVolunteers;
  List<VolunteerProfileModel> get assignedVolunteers => _assignedVolunteers;
  List<UserMicrotaskModel> get userMicrotasks => _userMicrotasks;

  // Getters de conveniência
  bool get isLoading => _state == TaskControllerState.loading;
  bool get hasError => _state == TaskControllerState.error;
  bool get isLoaded => _state == TaskControllerState.loaded;
  bool get isEmpty => _tasks.isEmpty;

  /// Carrega todas as tasks de um evento
  Future<void> loadEventTasks(String eventId) async {
    try {
      _setState(TaskControllerState.loading);
      _tasks = await _taskRepository.getEventTasks(eventId);
      _setState(TaskControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar tasks: ${e.toString()}');
    }
  }

  /// Carrega todas as microtasks de uma task
  Future<void> loadTaskMicrotasks(String taskId) async {
    try {
      _setState(TaskControllerState.loading);
      _microtasks = await _taskRepository.getTaskMicrotasks(taskId);
      _setState(TaskControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar microtasks: ${e.toString()}');
    }
  }

  /// Carrega a hierarquia completa de tasks e microtasks de um evento
  Future<void> loadEventTasksHierarchy(String eventId) async {
    try {
      _setState(TaskControllerState.loading);
      _tasksHierarchy = await _taskRepository.getEventTasksHierarchy(eventId);
      _tasks = _tasksHierarchy.keys.toList();
      _setState(TaskControllerState.loaded);
    } catch (e) {
      _setError('Erro ao carregar hierarquia: ${e.toString()}');
    }
  }

  /// Carrega o progresso de um evento
  Future<void> loadEventProgress(String eventId) async {
    try {
      _eventProgress = await _taskRepository.getEventProgress(eventId);
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar progresso: ${e.toString()}');
    }
  }

  /// Cria uma nova task
  Future<bool> createTask({
    required String eventId,
    required String title,
    required String description,
    required Priority priority,
    required String createdBy,
  }) async {
    try {
      _setState(TaskControllerState.loading);

      final task = TaskModel.create(
        eventId: eventId,
        title: title,
        description: description,
        priority: priority,
        createdBy: createdBy,
      );

      final createdTask = await _taskRepository.createTask(task);
      _tasks.add(createdTask);

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao criar task: ${e.toString()}');
      return false;
    }
  }

  /// Cria uma nova microtask
  Future<bool> createMicrotask({
    required String taskId,
    required String eventId,
    required String title,
    required String description,
    required List<String> requiredSkills,
    required List<String> requiredResources,
    required int estimatedHours,
    required Priority priority,
    required int maxVolunteers,
    required String createdBy,
    String notes = '',
  }) async {
    try {
      _setState(TaskControllerState.loading);

      final microtask = MicrotaskModel.create(
        taskId: taskId,
        eventId: eventId,
        title: title,
        description: description,
        requiredSkills: requiredSkills,
        requiredResources: requiredResources,
        estimatedHours: estimatedHours,
        priority: priority,
        maxVolunteers: maxVolunteers,
        createdBy: createdBy,
        notes: notes,
      );

      final createdMicrotask = await _taskRepository.createMicrotask(microtask);
      _microtasks.add(createdMicrotask);

      // Atualiza a hierarquia se estiver carregada
      if (_tasksHierarchy.isNotEmpty) {
        final task = _tasksHierarchy.keys.firstWhere((t) => t.id == taskId);
        _tasksHierarchy[task]?.add(createdMicrotask);
      }

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao criar microtask: ${e.toString()}');
      return false;
    }
  }

  /// Atualiza uma task
  Future<bool> updateTask(TaskModel task) async {
    try {
      _setState(TaskControllerState.loading);

      final updatedTask = await _taskRepository.updateTask(task);
      final index = _tasks.indexWhere((t) => t.id == task.id);

      if (index != -1) {
        _tasks[index] = updatedTask;
      }

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao atualizar task: ${e.toString()}');
      return false;
    }
  }

  /// Atualiza uma microtask
  Future<bool> updateMicrotask(MicrotaskModel microtask) async {
    try {
      _setState(TaskControllerState.loading);

      final updatedMicrotask = await _taskRepository.updateMicrotask(microtask);
      final index = _microtasks.indexWhere((m) => m.id == microtask.id);

      if (index != -1) {
        _microtasks[index] = updatedMicrotask;
      }

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao atualizar microtask: ${e.toString()}');
      return false;
    }
  }

  /// Deleta uma task
  Future<bool> deleteTask(String taskId) async {
    try {
      _setState(TaskControllerState.loading);

      await _taskRepository.deleteTask(taskId);
      _tasks.removeWhere((t) => t.id == taskId);

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao deletar task: ${e.toString()}');
      return false;
    }
  }

  /// Deleta uma microtask
  Future<bool> deleteMicrotask(String microtaskId) async {
    try {
      _setState(TaskControllerState.loading);

      await _taskRepository.deleteMicrotask(microtaskId);
      _microtasks.removeWhere((m) => m.id == microtaskId);

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao deletar microtask: ${e.toString()}');
      return false;
    }
  }

  /// Atribui um voluntário a uma microtask
  Future<bool> assignVolunteerToMicrotask(
    String userId,
    String microtaskId,
  ) async {
    try {
      _setState(TaskControllerState.loading);

      await _taskRepository.assignVolunteerToMicrotask(userId, microtaskId);

      // Atualiza a microtask na lista local
      final microtaskIndex = _microtasks.indexWhere((m) => m.id == microtaskId);
      if (microtaskIndex != -1) {
        final updatedMicrotask = await _taskRepository.getMicrotaskById(
          microtaskId,
        );
        if (updatedMicrotask != null) {
          _microtasks[microtaskIndex] = updatedMicrotask;
        }
      }

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao atribuir voluntário: ${e.toString()}');
      return false;
    }
  }

  /// Remove um voluntário de uma microtask
  Future<bool> removeVolunteerFromMicrotask(
    String userId,
    String microtaskId,
  ) async {
    try {
      _setState(TaskControllerState.loading);

      await _taskRepository.removeVolunteerFromMicrotask(userId, microtaskId);

      // Atualiza a microtask na lista local
      final microtaskIndex = _microtasks.indexWhere((m) => m.id == microtaskId);
      if (microtaskIndex != -1) {
        final updatedMicrotask = await _taskRepository.getMicrotaskById(
          microtaskId,
        );
        if (updatedMicrotask != null) {
          _microtasks[microtaskIndex] = updatedMicrotask;
        }
      }

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao remover voluntário: ${e.toString()}');
      return false;
    }
  }

  /// Atualiza o status de uma microtask para um usuário específico
  Future<bool> updateUserMicrotaskStatus(
    String microtaskId,
    String userId,
    UserMicrotaskStatus status,
  ) async {
    try {
      _setState(TaskControllerState.loading);

      await _taskRepository.updateUserMicrotaskStatus(
        microtaskId,
        userId,
        status,
      );

      _setState(TaskControllerState.loaded);
      return true;
    } catch (e) {
      _setError('Erro ao atualizar status: ${e.toString()}');
      return false;
    }
  }

  /// Carrega voluntários compatíveis com uma microtask
  Future<void> loadCompatibleVolunteers(String microtaskId) async {
    try {
      _compatibleVolunteers = await _taskRepository.getCompatibleVolunteers(
        microtaskId,
      );
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar voluntários compatíveis: ${e.toString()}');
    }
  }

  /// Carrega voluntários atribuídos a uma microtask
  Future<void> loadAssignedVolunteers(String microtaskId) async {
    try {
      _assignedVolunteers = await _taskRepository.getAssignedVolunteers(
        microtaskId,
      );
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar voluntários atribuídos: ${e.toString()}');
    }
  }

  /// Carrega as relações usuário-microtask para uma microtask
  Future<void> loadUserMicrotasks(String microtaskId) async {
    try {
      _userMicrotasks = await _taskRepository.getUserMicrotasks(microtaskId);
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar relações usuário-microtask: ${e.toString()}');
    }
  }

  /// Carrega as microtasks atribuídas a um usuário
  Future<void> loadUserAssignedMicrotasks(String userId, String eventId) async {
    try {
      _userMicrotasks = await _taskRepository.getUserAssignedMicrotasks(
        userId,
        eventId,
      );
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar microtasks do usuário: ${e.toString()}');
    }
  }

  /// Verifica se um voluntário pode ser atribuído a uma microtask
  Future<bool> canAssignVolunteer(String userId, String microtaskId) async {
    try {
      return await _taskRepository.canAssignVolunteer(userId, microtaskId);
    } catch (e) {
      return false;
    }
  }

  /// Busca uma task por ID
  TaskModel? getTaskById(String taskId) {
    try {
      return _tasks.firstWhere((task) => task.id == taskId);
    } catch (e) {
      return null;
    }
  }

  /// Busca uma microtask por ID
  MicrotaskModel? getMicrotaskById(String microtaskId) {
    try {
      return _microtasks.firstWhere((microtask) => microtask.id == microtaskId);
    } catch (e) {
      return null;
    }
  }

  /// Busca microtasks de uma task específica
  List<MicrotaskModel> getMicrotasksByTaskId(String taskId) {
    return _microtasks
        .where((microtask) => microtask.taskId == taskId)
        .toList();
  }

  /// Filtra tasks por status
  List<TaskModel> getTasksByStatus(TaskStatus status) {
    return _tasks.where((task) => task.status == status).toList();
  }

  /// Filtra microtasks por status
  List<MicrotaskModel> getMicrotasksByStatus(MicrotaskStatus status) {
    return _microtasks
        .where((microtask) => microtask.status == status)
        .toList();
  }

  /// Filtra tasks por prioridade
  List<TaskModel> getTasksByPriority(Priority priority) {
    return _tasks.where((task) => task.priority == priority).toList();
  }

  /// Filtra microtasks por prioridade
  List<MicrotaskModel> getMicrotasksByPriority(Priority priority) {
    return _microtasks
        .where((microtask) => microtask.priority == priority)
        .toList();
  }

  /// Limpa todos os dados
  void clearData() {
    _tasks.clear();
    _microtasks.clear();
    _tasksHierarchy.clear();
    _eventProgress.clear();
    _compatibleVolunteers.clear();
    _assignedVolunteers.clear();
    _userMicrotasks.clear();
    _setState(TaskControllerState.initial);
  }

  /// Limpa apenas o erro
  void clearError() {
    if (_state == TaskControllerState.error) {
      _errorMessage = null;
      _setState(TaskControllerState.initial);
    }
  }

  /// Atualiza o estado do controller
  void _setState(TaskControllerState newState) {
    _state = newState;
    if (newState != TaskControllerState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Define um erro e atualiza o estado
  void _setError(String error) {
    _errorMessage = error;
    _state = TaskControllerState.error;
    notifyListeners();
  }

  @override
  void dispose() {
    clearData();
    super.dispose();
  }
}
