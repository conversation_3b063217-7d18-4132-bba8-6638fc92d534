import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/microtask_model.dart';
import '../models/user_microtask_model.dart';
import '../models/volunteer_profile_model.dart';
import '../../core/constants/enums.dart';
import '../../core/exceptions/app_exceptions.dart';
import 'task_service.dart';

/// Serviço responsável pela lógica de negócio de atribuição de voluntários
/// Inclui validações de capacidade, duplicidade e compatibilidade
class AssignmentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TaskService _taskService = TaskService();
  final Uuid _uuid = const Uuid();

  // Referências das coleções
  CollectionReference get _microtasksCollection => _firestore.collection('microtasks');
  CollectionReference get _userMicrotasksCollection => _firestore.collection('user_microtasks');
  CollectionReference get _volunteerProfilesCollection => _firestore.collection('volunteer_profiles');

  /// Atribui um voluntário a uma microtask com todas as validações necessárias
  Future<UserMicrotaskModel> assignVolunteerToMicrotask(
    String userId, 
    String microtaskId
  ) async {
    try {
      // 1. Busca a microtask
      final microtask = await _taskService.getMicrotaskById(microtaskId);
      if (microtask == null) {
        throw NotFoundException('Microtask não encontrada');
      }

      // 2. Validação de capacidade máxima
      if (!microtask.canAssignMoreVolunteers) {
        throw ValidationException('Limite de voluntários atingido para esta microtask');
      }

      // 3. Validação de duplicidade
      if (microtask.isUserAssigned(userId)) {
        throw ValidationException('Voluntário já está atribuído a esta microtask');
      }

      // 4. Busca o perfil do voluntário para validações de compatibilidade
      final volunteerProfile = await _getVolunteerProfile(userId, microtask.eventId);
      if (volunteerProfile == null) {
        throw NotFoundException('Perfil de voluntário não encontrado');
      }

      // 5. Validação de compatibilidade de habilidades
      final hasRequiredSkills = _validateSkillCompatibility(
        microtask.requiredSkills, 
        volunteerProfile.skills
      );
      if (!hasRequiredSkills) {
        throw ValidationException('Voluntário não possui as habilidades necessárias');
      }

      // 6. Validação de compatibilidade de recursos
      final hasRequiredResources = _validateResourceCompatibility(
        microtask.requiredResources, 
        volunteerProfile.resources
      );
      if (!hasRequiredResources) {
        throw ValidationException('Voluntário não possui os recursos necessários');
      }

      // 7. Cria a relação usuário-microtask
      final userMicrotask = UserMicrotaskModel.create(
        userId: userId,
        microtaskId: microtaskId,
        eventId: microtask.eventId,
      );

      // 8. Salva a relação no Firestore
      final userMicrotaskId = _uuid.v4();
      final userMicrotaskWithId = userMicrotask.copyWith(id: userMicrotaskId);
      
      await _userMicrotasksCollection
          .doc(userMicrotaskId)
          .set(userMicrotaskWithId.toFirestore());

      // 9. Atualiza a lista de atribuídos na microtask
      final updatedAssignedTo = [...microtask.assignedTo, userId];
      final updatedMicrotask = microtask.copyWith(
        assignedTo: updatedAssignedTo,
        status: MicrotaskStatus.assigned,
        assignedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _taskService.updateMicrotask(updatedMicrotask);

      return userMicrotaskWithId;
    } catch (e) {
      if (e is AppException) rethrow;
      throw DatabaseException('Erro ao atribuir voluntário: ${e.toString()}');
    }
  }

  /// Remove um voluntário de uma microtask
  Future<void> removeVolunteerFromMicrotask(String userId, String microtaskId) async {
    try {
      // 1. Busca a microtask
      final microtask = await _taskService.getMicrotaskById(microtaskId);
      if (microtask == null) {
        throw NotFoundException('Microtask não encontrada');
      }

      // 2. Verifica se o usuário está atribuído
      if (!microtask.isUserAssigned(userId)) {
        throw ValidationException('Voluntário não está atribuído a esta microtask');
      }

      // 3. Busca e remove a relação usuário-microtask
      final userMicrotaskQuery = await _userMicrotasksCollection
          .where('microtaskId', isEqualTo: microtaskId)
          .where('userId', isEqualTo: userId)
          .get();

      if (userMicrotaskQuery.docs.isNotEmpty) {
        await userMicrotaskQuery.docs.first.reference.delete();
      }

      // 4. Atualiza a lista de atribuídos na microtask
      final updatedAssignedTo = microtask.assignedTo.where((id) => id != userId).toList();
      final updatedMicrotask = microtask.copyWith(
        assignedTo: updatedAssignedTo,
        status: updatedAssignedTo.isEmpty ? MicrotaskStatus.pending : microtask.status,
        updatedAt: DateTime.now(),
      );

      await _taskService.updateMicrotask(updatedMicrotask);
    } catch (e) {
      if (e is AppException) rethrow;
      throw DatabaseException('Erro ao remover voluntário: ${e.toString()}');
    }
  }

  /// Busca voluntários compatíveis com uma microtask
  Future<List<VolunteerProfileModel>> getCompatibleVolunteers(String microtaskId) async {
    try {
      final microtask = await _taskService.getMicrotaskById(microtaskId);
      if (microtask == null) {
        throw NotFoundException('Microtask não encontrada');
      }

      // Busca todos os voluntários do evento
      final allVolunteers = await _getEventVolunteers(microtask.eventId);

      // Filtra voluntários compatíveis
      final compatibleVolunteers = <VolunteerProfileModel>[];
      
      for (final volunteer in allVolunteers) {
        // Verifica se já está atribuído
        if (microtask.isUserAssigned(volunteer.userId)) {
          continue;
        }

        // Verifica compatibilidade de habilidades
        final hasSkills = _validateSkillCompatibility(
          microtask.requiredSkills, 
          volunteer.skills
        );

        // Verifica compatibilidade de recursos
        final hasResources = _validateResourceCompatibility(
          microtask.requiredResources, 
          volunteer.resources
        );

        if (hasSkills && hasResources) {
          compatibleVolunteers.add(volunteer);
        }
      }

      return compatibleVolunteers;
    } catch (e) {
      if (e is AppException) rethrow;
      throw DatabaseException('Erro ao buscar voluntários compatíveis: ${e.toString()}');
    }
  }

  /// Busca voluntários já atribuídos a uma microtask
  Future<List<VolunteerProfileModel>> getAssignedVolunteers(String microtaskId) async {
    try {
      final microtask = await _taskService.getMicrotaskById(microtaskId);
      if (microtask == null) {
        throw NotFoundException('Microtask não encontrada');
      }

      final assignedVolunteers = <VolunteerProfileModel>[];
      
      for (final userId in microtask.assignedTo) {
        final volunteer = await _getVolunteerProfile(userId, microtask.eventId);
        if (volunteer != null) {
          assignedVolunteers.add(volunteer);
        }
      }

      return assignedVolunteers;
    } catch (e) {
      if (e is AppException) rethrow;
      throw DatabaseException('Erro ao buscar voluntários atribuídos: ${e.toString()}');
    }
  }

  /// Verifica se um voluntário pode ser atribuído a uma microtask
  Future<bool> canAssignVolunteer(String userId, String microtaskId) async {
    try {
      final microtask = await _taskService.getMicrotaskById(microtaskId);
      if (microtask == null) return false;

      // Verifica capacidade
      if (!microtask.canAssignMoreVolunteers) return false;

      // Verifica duplicidade
      if (microtask.isUserAssigned(userId)) return false;

      // Verifica compatibilidade
      final volunteerProfile = await _getVolunteerProfile(userId, microtask.eventId);
      if (volunteerProfile == null) return false;

      final hasSkills = _validateSkillCompatibility(
        microtask.requiredSkills, 
        volunteerProfile.skills
      );

      final hasResources = _validateResourceCompatibility(
        microtask.requiredResources, 
        volunteerProfile.resources
      );

      return hasSkills && hasResources;
    } catch (e) {
      return false;
    }
  }

  /// Busca o perfil de um voluntário em um evento específico
  Future<VolunteerProfileModel?> _getVolunteerProfile(String userId, String eventId) async {
    try {
      final querySnapshot = await _volunteerProfilesCollection
          .where('userId', isEqualTo: userId)
          .where('eventId', isEqualTo: eventId)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return VolunteerProfileModel.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      throw DatabaseException('Erro ao buscar perfil do voluntário: ${e.toString()}');
    }
  }

  /// Busca todos os voluntários de um evento
  Future<List<VolunteerProfileModel>> _getEventVolunteers(String eventId) async {
    try {
      final querySnapshot = await _volunteerProfilesCollection
          .where('eventId', isEqualTo: eventId)
          .get();

      return querySnapshot.docs
          .map((doc) => VolunteerProfileModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw DatabaseException('Erro ao buscar voluntários do evento: ${e.toString()}');
    }
  }

  /// Valida se o voluntário possui as habilidades necessárias
  bool _validateSkillCompatibility(List<String> requiredSkills, List<String> volunteerSkills) {
    if (requiredSkills.isEmpty) return true;
    
    return requiredSkills.every((skill) => volunteerSkills.contains(skill));
  }

  /// Valida se o voluntário possui os recursos necessários
  bool _validateResourceCompatibility(List<String> requiredResources, List<String> volunteerResources) {
    if (requiredResources.isEmpty) return true;
    
    return requiredResources.every((resource) => volunteerResources.contains(resource));
  }
}
