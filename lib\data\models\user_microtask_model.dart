import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/constants/enums.dart';

/// Modelo de dados para representar a relação entre um usuário e uma microtask
/// Controla o status individual de cada voluntário em uma microtask específica
/// Permite que múltiplos voluntários trabalhem na mesma microtask com status independentes
class UserMicrotaskModel {
  final String id;
  final String userId;
  final String microtaskId;
  final String eventId;
  final UserMicrotaskStatus status;
  final DateTime assignedAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final String notes;
  final int hoursWorked;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserMicrotaskModel({
    required this.id,
    required this.userId,
    required this.microtaskId,
    required this.eventId,
    required this.status,
    required this.assignedAt,
    this.startedAt,
    this.completedAt,
    required this.notes,
    required this.hoursWorked,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Cria uma cópia da relação usuário-microtask com campos atualizados
  UserMicrotaskModel copyWith({
    String? id,
    String? userId,
    String? microtaskId,
    String? eventId,
    UserMicrotaskStatus? status,
    DateTime? assignedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? notes,
    int? hoursWorked,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserMicrotaskModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      microtaskId: microtaskId ?? this.microtaskId,
      eventId: eventId ?? this.eventId,
      status: status ?? this.status,
      assignedAt: assignedAt ?? this.assignedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      notes: notes ?? this.notes,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Converte o modelo para Map para salvar no Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'microtaskId': microtaskId,
      'eventId': eventId,
      'status': status.value,
      'assignedAt': Timestamp.fromDate(assignedAt),
      'startedAt': startedAt != null ? Timestamp.fromDate(startedAt!) : null,
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'notes': notes,
      'hoursWorked': hoursWorked,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// Cria uma instância a partir de um documento do Firestore
  factory UserMicrotaskModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserMicrotaskModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      microtaskId: data['microtaskId'] ?? '',
      eventId: data['eventId'] ?? '',
      status: UserMicrotaskStatus.fromString(data['status'] ?? 'assigned'),
      assignedAt: (data['assignedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      startedAt: (data['startedAt'] as Timestamp?)?.toDate(),
      completedAt: (data['completedAt'] as Timestamp?)?.toDate(),
      notes: data['notes'] ?? '',
      hoursWorked: data['hoursWorked'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  /// Cria uma instância a partir de um Map
  factory UserMicrotaskModel.fromMap(Map<String, dynamic> map) {
    return UserMicrotaskModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      microtaskId: map['microtaskId'] ?? '',
      eventId: map['eventId'] ?? '',
      status: UserMicrotaskStatus.fromString(map['status'] ?? 'assigned'),
      assignedAt: map['assignedAt'] is DateTime ? map['assignedAt'] : DateTime.now(),
      startedAt: map['startedAt'] is DateTime ? map['startedAt'] : null,
      completedAt: map['completedAt'] is DateTime ? map['completedAt'] : null,
      notes: map['notes'] ?? '',
      hoursWorked: map['hoursWorked'] ?? 0,
      createdAt: map['createdAt'] is DateTime ? map['createdAt'] : DateTime.now(),
      updatedAt: map['updatedAt'] is DateTime ? map['updatedAt'] : DateTime.now(),
    );
  }

  /// Converte para Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'microtaskId': microtaskId,
      'eventId': eventId,
      'status': status.value,
      'assignedAt': assignedAt,
      'startedAt': startedAt,
      'completedAt': completedAt,
      'notes': notes,
      'hoursWorked': hoursWorked,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// Factory para criar uma nova relação usuário-microtask
  factory UserMicrotaskModel.create({
    required String userId,
    required String microtaskId,
    required String eventId,
    String notes = '',
  }) {
    final now = DateTime.now();
    
    return UserMicrotaskModel(
      id: '', // Será definido pelo Firestore
      userId: userId,
      microtaskId: microtaskId,
      eventId: eventId,
      status: UserMicrotaskStatus.assigned,
      assignedAt: now,
      notes: notes,
      hoursWorked: 0,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Verifica se o usuário completou a microtask
  bool get isCompleted => status == UserMicrotaskStatus.completed;

  /// Verifica se o usuário está trabalhando na microtask
  bool get isInProgress => status == UserMicrotaskStatus.inProgress;

  /// Verifica se a microtask foi apenas atribuída ao usuário
  bool get isAssigned => status == UserMicrotaskStatus.assigned;

  /// Verifica se a microtask foi cancelada para o usuário
  bool get isCancelled => status == UserMicrotaskStatus.cancelled;

  /// Calcula o tempo trabalhado em horas (se completado)
  double? get actualHoursWorked {
    if (startedAt != null && completedAt != null) {
      final duration = completedAt!.difference(startedAt!);
      return duration.inMinutes / 60.0;
    }
    return null;
  }

  /// Valida os dados da relação usuário-microtask
  List<String> validate() {
    final errors = <String>[];
    
    if (userId.trim().isEmpty) {
      errors.add('ID do usuário é obrigatório');
    }
    
    if (microtaskId.trim().isEmpty) {
      errors.add('ID da microtask é obrigatório');
    }
    
    if (eventId.trim().isEmpty) {
      errors.add('ID do evento é obrigatório');
    }
    
    if (hoursWorked < 0) {
      errors.add('Horas trabalhadas não podem ser negativas');
    }
    
    if (hoursWorked > 168) { // 1 semana
      errors.add('Horas trabalhadas não podem exceder 168 horas');
    }
    
    // Validações de datas
    if (startedAt != null && startedAt!.isBefore(assignedAt)) {
      errors.add('Data de início não pode ser anterior à data de atribuição');
    }
    
    if (completedAt != null && startedAt != null && completedAt!.isBefore(startedAt!)) {
      errors.add('Data de conclusão não pode ser anterior à data de início');
    }
    
    if (completedAt != null && completedAt!.isBefore(assignedAt)) {
      errors.add('Data de conclusão não pode ser anterior à data de atribuição');
    }
    
    // Validações de status
    if (status == UserMicrotaskStatus.inProgress && startedAt == null) {
      errors.add('Data de início é obrigatória quando status é "Em Progresso"');
    }
    
    if (status == UserMicrotaskStatus.completed && completedAt == null) {
      errors.add('Data de conclusão é obrigatória quando status é "Concluída"');
    }
    
    return errors;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserMicrotaskModel &&
        other.id == id &&
        other.userId == userId &&
        other.microtaskId == microtaskId &&
        other.eventId == eventId &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      microtaskId,
      eventId,
      status,
    );
  }

  @override
  String toString() {
    return 'UserMicrotaskModel(userId: $userId, microtaskId: $microtaskId, status: ${status.displayName}, hours: $hoursWorked)';
  }
}
