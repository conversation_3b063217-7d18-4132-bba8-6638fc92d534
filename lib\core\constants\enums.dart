/// Enums centralizados do sistema ConTask
/// Contém todos os enums utilizados na aplicação para garantir consistência

/// Enum para representar o status de uma Task
enum TaskStatus {
  pending('pending'),
  inProgress('in_progress'),
  completed('completed');

  const TaskStatus(this.value);
  final String value;

  static TaskStatus fromString(String value) {
    return TaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TaskStatus.pending,
    );
  }

  String get displayName {
    switch (this) {
      case TaskStatus.pending:
        return 'Pendente';
      case TaskStatus.inProgress:
        return 'Em Progresso';
      case TaskStatus.completed:
        return 'Concluída';
    }
  }
}

/// Enum para representar o status de uma Microtask
enum MicrotaskStatus {
  pending('pending'),
  assigned('assigned'),
  inProgress('in_progress'),
  completed('completed'),
  cancelled('cancelled');

  const MicrotaskStatus(this.value);
  final String value;

  static MicrotaskStatus fromString(String value) {
    return MicrotaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => MicrotaskStatus.pending,
    );
  }

  String get displayName {
    switch (this) {
      case MicrotaskStatus.pending:
        return 'Pendente';
      case MicrotaskStatus.assigned:
        return 'Atribuída';
      case MicrotaskStatus.inProgress:
        return 'Em Progresso';
      case MicrotaskStatus.completed:
        return 'Concluída';
      case MicrotaskStatus.cancelled:
        return 'Cancelada';
    }
  }
}

/// Enum para representar níveis de prioridade
enum Priority {
  high('high'),
  medium('medium'),
  low('low');

  const Priority(this.value);
  final String value;

  static Priority fromString(String value) {
    return Priority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => Priority.medium,
    );
  }

  String get displayName {
    switch (this) {
      case Priority.high:
        return 'Alta';
      case Priority.medium:
        return 'Média';
      case Priority.low:
        return 'Baixa';
    }
  }
}

/// Enum para representar o status do evento
enum EventStatus {
  active('active'),
  completed('completed'),
  cancelled('cancelled');

  const EventStatus(this.value);
  final String value;

  static EventStatus fromString(String value) {
    return EventStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => EventStatus.active,
    );
  }

  String get displayName {
    switch (this) {
      case EventStatus.active:
        return 'Ativo';
      case EventStatus.completed:
        return 'Concluído';
      case EventStatus.cancelled:
        return 'Cancelado';
    }
  }
}

/// Enum para representar o papel do usuário no evento
enum UserRole {
  creator('creator'),
  manager('manager'),
  volunteer('volunteer'),
  none('none');

  const UserRole(this.value);
  final String value;

  String get displayName {
    switch (this) {
      case UserRole.creator:
        return 'Criador';
      case UserRole.manager:
        return 'Gerenciador';
      case UserRole.volunteer:
        return 'Voluntário';
      case UserRole.none:
        return 'Não participante';
    }
  }
}

/// Enum para representar o status individual de um usuário em uma microtask
enum UserMicrotaskStatus {
  assigned('assigned'),
  inProgress('in_progress'),
  completed('completed'),
  cancelled('cancelled');

  const UserMicrotaskStatus(this.value);
  final String value;

  static UserMicrotaskStatus fromString(String value) {
    return UserMicrotaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => UserMicrotaskStatus.assigned,
    );
  }

  String get displayName {
    switch (this) {
      case UserMicrotaskStatus.assigned:
        return 'Atribuída';
      case UserMicrotaskStatus.inProgress:
        return 'Em Progresso';
      case UserMicrotaskStatus.completed:
        return 'Concluída';
      case UserMicrotaskStatus.cancelled:
        return 'Cancelada';
    }
  }
}
