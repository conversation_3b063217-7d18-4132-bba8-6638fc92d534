import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/microtask_model.dart';
import '../event/skill_chip.dart';

/// Widget de card para exibir informações de uma Microtask
class MicrotaskCard extends StatelessWidget {
  final MicrotaskModel microtask;
  final VoidCallback? onTap;
  final bool showDetails;
  final bool showVolunteers;
  final String? currentUserId;

  const MicrotaskCard({
    super.key,
    required this.microtask,
    this.onTap,
    this.showDetails = true,
    this.showVolunteers = true,
    this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: AppDimensions.elevationSm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingMd),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header com título e status
              Row(
                children: [
                  _buildStatusIcon(),
                  const SizedBox(width: AppDimensions.spacingSm),
                  Expanded(
                    child: Text(
                      microtask.title,
                      style: const TextStyle(
                        fontSize: AppDimensions.fontSizeMd,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildPriorityChip(),
                ],
              ),
              
              const SizedBox(height: AppDimensions.spacingSm),
              
              // Descrição
              Text(
                microtask.description,
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeSm,
                  color: AppColors.textSecondary,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (showDetails) ...[
                const SizedBox(height: AppDimensions.spacingMd),
                _buildDetailsSection(),
              ],
              
              if (microtask.requiredSkills.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.spacingMd),
                _buildSkillsSection(),
              ],
              
              if (microtask.requiredResources.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.spacingMd),
                _buildResourcesSection(),
              ],
              
              if (showVolunteers && microtask.assignedTo.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.spacingMd),
                _buildVolunteersSection(),
              ],
              
              const SizedBox(height: AppDimensions.spacingSm),
              
              // Footer
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  /// Constrói o ícone de status
  Widget _buildStatusIcon() {
    IconData icon;
    Color color;
    
    switch (microtask.status) {
      case MicrotaskStatus.pending:
        icon = Icons.schedule;
        color = AppColors.warning;
        break;
      case MicrotaskStatus.assigned:
        icon = Icons.assignment_ind;
        color = AppColors.info;
        break;
      case MicrotaskStatus.inProgress:
        icon = Icons.play_circle;
        color = AppColors.primary;
        break;
      case MicrotaskStatus.completed:
        icon = Icons.check_circle;
        color = AppColors.success;
        break;
      case MicrotaskStatus.cancelled:
        icon = Icons.cancel;
        color = AppColors.error;
        break;
    }
    
    return Icon(icon, color: color, size: 20);
  }

  /// Constrói o chip de prioridade
  Widget _buildPriorityChip() {
    Color backgroundColor;
    Color textColor;
    
    switch (microtask.priority) {
      case Priority.high:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        break;
      case Priority.medium:
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        break;
      case Priority.low:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSm,
        vertical: AppDimensions.paddingXs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: textColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        microtask.priority.displayName,
        style: TextStyle(
          fontSize: AppDimensions.fontSizeXs,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  /// Constrói a seção de detalhes
  Widget _buildDetailsSection() {
    return Row(
      children: [
        // Horas estimadas
        Icon(
          Icons.schedule,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: AppDimensions.spacingXs),
        Text(
          '${microtask.estimatedHours}h',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeSm,
            color: AppColors.textSecondary,
          ),
        ),
        
        const SizedBox(width: AppDimensions.spacingMd),
        
        // Voluntários
        Icon(
          Icons.people,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: AppDimensions.spacingXs),
        Text(
          '${microtask.assignedTo.length}/${microtask.maxVolunteers}',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeSm,
            color: AppColors.textSecondary,
          ),
        ),
        
        const Spacer(),
        
        // Indicador de disponibilidade
        if (microtask.canAssignMoreVolunteers) ...[
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingSm,
              vertical: AppDimensions.paddingXs,
            ),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
            ),
            child: const Text(
              'Disponível',
              style: TextStyle(
                fontSize: AppDimensions.fontSizeXs,
                fontWeight: FontWeight.w600,
                color: AppColors.success,
              ),
            ),
          ),
        ] else ...[
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingSm,
              vertical: AppDimensions.paddingXs,
            ),
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
            ),
            child: const Text(
              'Completa',
              style: TextStyle(
                fontSize: AppDimensions.fontSizeXs,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Constrói a seção de habilidades
  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Habilidades:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: microtask.requiredSkills.map((skill) {
            return SkillChip(
              label: skill,
              isSelected: false,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              textColor: AppColors.primary,
              borderColor: AppColors.primary.withValues(alpha: 0.3),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói a seção de recursos
  Widget _buildResourcesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recursos:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: microtask.requiredResources.map((resource) {
            return SkillChip(
              label: resource,
              isSelected: false,
              backgroundColor: AppColors.secondary.withValues(alpha: 0.1),
              textColor: AppColors.secondary,
              borderColor: AppColors.secondary.withValues(alpha: 0.3),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói a seção de voluntários
  Widget _buildVolunteersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Voluntários:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: microtask.assignedTo.map((userId) {
            final isCurrentUser = currentUserId == userId;
            
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: isCurrentUser 
                    ? AppColors.primary 
                    : AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                isCurrentUser ? 'Você' : 'Voluntário', // TODO: Buscar nome real
                style: TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  color: isCurrentUser 
                      ? AppColors.textOnPrimary 
                      : AppColors.primary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói o footer
  Widget _buildFooter() {
    return Row(
      children: [
        // Status textual
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingSm,
            vertical: AppDimensions.paddingXs,
          ),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
          ),
          child: Text(
            microtask.status.displayName,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXs,
              fontWeight: FontWeight.w600,
              color: _getStatusColor(),
            ),
          ),
        ),
        
        const Spacer(),
        
        // Data de criação
        Text(
          'Criada ${_formatDate(microtask.createdAt)}',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// Retorna a cor baseada no status
  Color _getStatusColor() {
    switch (microtask.status) {
      case MicrotaskStatus.pending:
        return AppColors.warning;
      case MicrotaskStatus.assigned:
        return AppColors.info;
      case MicrotaskStatus.inProgress:
        return AppColors.primary;
      case MicrotaskStatus.completed:
        return AppColors.success;
      case MicrotaskStatus.cancelled:
        return AppColors.error;
    }
  }

  /// Formata a data de forma amigável
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'há ${difference.inDays} dia${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'há ${difference.inHours} hora${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'há ${difference.inMinutes} minuto${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'agora';
    }
  }
}
