FASE 3: Plano de Implementação – Sistema de Tarefas
🎯 Visão Geral da Fase
Esta fase foca na implementação do sistema de gerenciamento de tarefas, que é o núcleo funcional do aplicativo. O objetivo é permitir que Gerenciadores de Eventos criem uma hierarquia de Tasks (agrupadores) e Microtasks (ações executáveis), e atribuam múltiplos voluntários a cada Microtask.

As principais funcionalidades incluem:

Criação de Tasks e Microtasks.

Atribuição de múltiplos voluntários a Microtasks, com controle de capacidade.

Visualização hierárquica do progresso das tarefas.

Gerenciamento de voluntários e suas atribuições.

Controle de status colaborativo.

📋 Funcionalidades a Implementar
1. Modelos de Dados e Enums
[ ] task_model.dart: Modelo para Tasks (agrupador).

[ ] microtask_model.dart: Modelo para Microtasks, com assignedTo como List<String> e maxVolunteers.

[ ] user_microtask_model.dart: Modelo para a relação entre usuário e microtask, controlando o status individual.

[ ] Enums: TaskStatus, MicrotaskStatus, Priority.

2. Repositórios e Serviços
[ ] task_repository.dart: Abstração para o TaskService.

[ ] task_service.dart: Lógica de CRUD para tasks e microtasks no Firestore.

[ ] assignment_service.dart: Serviço para a lógica de negócio de atribuição de voluntários, incluindo validações de capacidade e duplicidade.

3. Controller de Estado
[ ] task_controller.dart: Gerenciamento de estado (usando Provider) para Tasks e Microtasks, incluindo busca, criação e atualização.

[ ] volunteer_controller.dart: Expansão para gerenciar a atribuição de voluntários.

4. Telas e Navegação
[ ] event_details_screen.dart: Tela principal com sistema de Tabs para organizar as funcionalidades.

[ ] Tab "Evento": Informações gerais (já existentes).

[ ] Tab "Criar Tasks" (Apenas Gerenciadores).

[ ] Tab "Acompanhar Tasks".

[ ] Tab "Gerenciar Voluntários" (Apenas Gerenciadores).

[ ] create_tasks_screen.dart: Formulário para criar Tasks e Microtasks.

[ ] track_tasks_screen.dart: Tela para visualizar a hierarquia e o progresso das tarefas.

[ ] manage_volunteers_screen.dart: Tela para listar voluntários e atribuí-los a microtasks.

5. Widgets Reutilizáveis
[ ] task_card.dart: Card para exibir uma Task e suas Microtasks aninhadas.

[ ] microtask_card.dart: Card para exibir detalhes de uma Microtask.

[ ] volunteer_assignment_card.dart: Card para exibir um voluntário na tela de gerenciamento.

[ ] volunteer_list_widget.dart: Widget para exibir a lista de voluntários atribuídos a uma microtask.

📂 Estrutura de Arquivos (Novos Arquivos)
lib/
├── data/
│   ├── models/
│   │   ├── task_model.dart             # NOVO
│   │   ├── microtask_model.dart        # NOVO
│   │   └── user_microtask_model.dart   # NOVO
│   ├── repositories/
│   │   └── task_repository.dart        # NOVO
│   └── services/
│       ├── task_service.dart           # NOVO
│       └── assignment_service.dart     # NOVO
├── presentation/
│   ├── controllers/
│   │   ├── task_controller.dart        # NOVO
│   │   └── volunteer_controller.dart   # ATUALIZAR
│   ├── screens/
│   │   └── event/
│   │       ├── event_details_screen.dart       # ATUALIZAR
│   │       ├── create_tasks_screen.dart      # NOVO
│   │       ├── track_tasks_screen.dart       # NOVO
│   │       └── manage_volunteers_screen.dart # NOVO
│   └── widgets/
│       ├── task/                       # NOVA PASTA
│       │   ├── task_card.dart
│       │   ├── microtask_card.dart
│       │   └── volunteer_list_widget.dart
│       └── volunteer/
│           └── volunteer_assignment_card.dart
└── core/
    └── constants/
        └── enums.dart                  # ATUALIZAR

🚀 Plano de Implementação Detalhado (Passo a Passo)
Passo 1: Estrutura de Dados (Models e Enums)
Atividade 1.1: Atualizar core/constants/enums.dart com TaskStatus, MicrotaskStatus e Priority.

Atividade 1.2: Criar data/models/task_model.dart conforme a SPEC_GERAL.md.

Atividade 1.3: Criar data/models/microtask_model.dart, garantindo que assignedTo seja List<String> e que maxVolunteers esteja presente.

Atividade 1.4: Criar data/models/user_microtask_model.dart para gerenciar o status de conclusão individual de cada voluntário em uma microtask.

Passo 2: Camada de Dados (Repository e Services)
Atividade 2.1: Implementar data/services/task_service.dart com métodos para:

createTask(Task task)

createMicrotask(Microtask microtask)

getTasks(String eventId)

getMicrotasks(String taskId)

updateMicrotaskStatus(String microtaskId, String userId, MicrotaskStatus status)

Atividade 2.2: Criar data/repositories/task_repository.dart para abstrair as chamadas ao TaskService.

Passo 3: Camada de Controle (Controllers)
Atividade 3.1: Criar presentation/controllers/task_controller.dart.

Adicionar métodos para carregar Tasks e Microtasks do repositório.

Implementar a lógica para criar novas Tasks e Microtasks.

Gerenciar o estado de loading e erro.

Passo 4: Navegação e Tela de Detalhes do Evento
Atividade 4.1: Modificar presentation/screens/event/event_details_screen.dart para usar um TabBar ou BottomNavigationBar.

Atividade 4.2: Criar as 3 novas telas (create_tasks_screen.dart, track_tasks_screen.dart, manage_volunteers_screen.dart) como widgets Stateless vazios, apenas para configurar a navegação.

Atividade 4.3: Implementar a lógica de visibilidade das tabs "Criar Tasks" e "Gerenciar Voluntários" apenas para usuários com o papel de manager.

Passo 5: Implementação da Tela "Criar Tasks"
Atividade 5.1: Desenvolver a UI de create_tasks_screen.dart com dois formulários distintos: um para Task e outro para Microtask.

Atividade 5.2: O formulário de Microtask deve incluir um DropdownButton para selecionar a Task pai.

Atividade 5.3: Implementar o campo "Número máximo de voluntários" no formulário de Microtask.

Atividade 5.4: Conectar a UI ao TaskController para salvar os dados no Firestore.

Passo 6: Implementação da Tela "Acompanhar Tasks"
Atividade 6.1: Desenvolver a UI de track_tasks_screen.dart.

Atividade 6.2: Usar um ListView de ExpansionTile ou similar para criar a visualização hierárquica (Tasks que expandem para mostrar Microtasks).

Atividade 6.3: Criar o widget task_card.dart e microtask_card.dart para exibir as informações.

Atividade 6.4: No microtask_card.dart, incluir o volunteer_list_widget.dart para mostrar os avatares ou nomes dos voluntários atribuídos.

Passo 7: Implementação da Tela "Gerenciar Voluntários"
Atividade 7.1: Desenvolver a UI de manage_volunteers_screen.dart para listar todos os voluntários do evento.

Atividade 7.2: Criar o widget volunteer_assignment_card.dart, que exibe informações do voluntário e um botão "Atribuir Microtask".

Atividade 7.3: Ao clicar em "Atribuir", abrir um Dialog ou navegar para uma nova tela que lista as Microtasks disponíveis.

Passo 8: Lógica de Atribuição Múltipla
Atividade 8.1: Criar data/services/assignment_service.dart.

Atividade 8.2: Implementar o método assignVolunteerToMicrotask(String userId, String microtaskId).

Validação 1: Verificar se a microtask já atingiu o maxVolunteers.

Validação 2: Verificar se o userId já está na lista assignedTo para evitar duplicidade.

Atividade 8.3: Integrar este serviço ao Dialog de atribuição na tela "Gerenciar Voluntários".

🧪 Plano de Testes (Pós-Implementação)
Teste 1: Fluxo de Criação de Tarefas
Objetivo: Validar a criação de Tasks e Microtasks.

Passos:

Acessar um evento como Gerenciador.

Ir para a tab "Criar Tasks".

Criar uma Task "Logística".

Criar uma Microtask "Montar Palco", associada à Task "Logística", com maxVolunteers = 3.

Resultado Esperado: Task e Microtask visíveis na tab "Acompanhar Tasks" e salvas corretamente no Firestore.

Teste 2: Fluxo de Atribuição de Múltiplos Voluntários
Objetivo: Validar a atribuição de vários voluntários a uma microtask.

Passos:

Acessar a tab "Gerenciar Voluntários".

Selecionar o Voluntário A e atribuí-lo à microtask "Montar Palco".

Selecionar o Voluntário B e atribuí-lo à mesma microtask.

Selecionar o Voluntário C e atribuí-lo à mesma microtask.

Resultado Esperado: Os 3 voluntários devem aparecer na lista de atribuídos da microtask. O assignedTo no Firestore deve conter os 3 userIds.

Teste 3: Validação de Limite de Voluntários
Objetivo: Garantir que o sistema impeça a atribuição além da capacidade.

Passos:

Com a microtask "Montar Palco" já com 3 voluntários (limite).

Tentar atribuir o Voluntário D à mesma microtask.

Resultado Esperado: O sistema deve exibir uma mensagem de erro ("Limite de voluntários atingido") e impedir a atribuição.

Teste 4: Validação de Atribuição Dupla
Objetivo: Impedir que o mesmo voluntário seja atribuído duas vezes.

Passos:

Remover um voluntário da microtask "Montar Palco" (deixando 2).

Tentar atribuir o Voluntário A (que já está na lista) novamente.

Resultado Esperado: O sistema deve exibir um erro ("Voluntário já atribuído a esta tarefa") e impedir a ação.

Teste 5: Controle de Acesso por Papel
Objetivo: Validar que apenas gerenciadores podem acessar as telas de gerenciamento.

Passos:

Fazer login como um usuário com papel de volunteer.

Acessar a tela de detalhes do evento.

Resultado Esperado: As tabs "Criar Tasks" e "Gerenciar Voluntários" não devem estar visíveis.

✅ Critérios de Aceitação
[ ] Gerenciadores podem criar e organizar Tasks e Microtasks.

[ ] Gerenciadores podem atribuir múltiplos voluntários a uma única Microtask.

[ ] O sistema valida e respeita o limite máximo de voluntários por Microtask.

[ ] O sistema impede a atribuição duplicada de um mesmo voluntário à mesma Microtask.

[ ] Todos os participantes do evento podem visualizar o progresso das tarefas na tab "Acompanhar Tasks".

[ ] A interface é clara e exibe corretamente a hierarquia de tarefas e os voluntários atribuídos.

[ ] O acesso às funcionalidades de gerenciamento é restrito aos managers.