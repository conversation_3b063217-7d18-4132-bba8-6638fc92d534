import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/event_model.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/event_controller.dart';
import '../../controllers/task_controller.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/event/skill_chip.dart';

/// Tela para criar Tasks e Microtasks
/// Disponível apenas para gerenciadores de eventos
class CreateTasksScreen extends StatefulWidget {
  final String eventId;

  const CreateTasksScreen({super.key, required this.eventId});

  @override
  State<CreateTasksScreen> createState() => _CreateTasksScreenState();
}

class _CreateTasksScreenState extends State<CreateTasksScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  EventModel? _currentEvent;

  // Controladores para Task
  final _taskTitleController = TextEditingController();
  final _taskDescriptionController = TextEditingController();
  Priority _taskPriority = Priority.medium;

  // Controladores para Microtask
  final _microtaskTitleController = TextEditingController();
  final _microtaskDescriptionController = TextEditingController();
  final _microtaskNotesController = TextEditingController();
  final _estimatedHoursController = TextEditingController();
  final _maxVolunteersController = TextEditingController();
  Priority _microtaskPriority = Priority.medium;
  String? _selectedTaskId;
  final List<String> _selectedSkills = [];
  final List<String> _selectedResources = [];

  // Estados
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _estimatedHoursController.text = '1';
    _maxVolunteersController.text = '1';
    _loadEventData();
  }

  /// Carrega os dados do evento
  Future<void> _loadEventData() async {
    final eventController = context.read<EventController>();

    setState(() => _isLoading = true);

    try {
      final event = await eventController.loadEvent(widget.eventId);
      if (event != null) {
        setState(() => _currentEvent = event);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Erro ao carregar evento: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const LoadingWidget(message: 'Carregando...');
    }

    if (_currentEvent == null) {
      return const Center(child: Text('Evento não encontrado'));
    }

    return Consumer<TaskController>(
      builder: (context, taskController, child) {
        return Scaffold(
          backgroundColor: AppColors.background,
          body: Column(
            children: [
              // Header com tabs
              Container(
                color: AppColors.surface,
                child: TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(icon: Icon(Icons.folder_outlined), text: 'Criar Task'),
                    Tab(
                      icon: Icon(Icons.task_outlined),
                      text: 'Criar Microtask',
                    ),
                  ],
                  labelColor: AppColors.primary,
                  unselectedLabelColor: AppColors.textSecondary,
                  indicatorColor: AppColors.primary,
                ),
              ),

              // Conteúdo das tabs
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildCreateTaskTab(taskController),
                    _buildCreateMicrotaskTab(taskController),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Constrói a tab para criar Task
  Widget _buildCreateTaskTab(TaskController taskController) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Criar Nova Task',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXl,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingSm),

          const Text(
            'Tasks servem como agrupadores organizacionais para Microtasks relacionadas.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingLg),

          // Formulário da Task
          _buildTaskForm(taskController),
        ],
      ),
    );
  }

  /// Constrói a tab para criar Microtask
  Widget _buildCreateMicrotaskTab(TaskController taskController) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Criar Nova Microtask',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXl,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingSm),

          const Text(
            'Microtasks são as ações executáveis que podem ter voluntários atribuídos.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingLg),

          // Formulário da Microtask
          _buildMicrotaskForm(taskController),
        ],
      ),
    );
  }

  /// Constrói o formulário para criar Task
  Widget _buildTaskForm(TaskController taskController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Título
        CustomTextField(
          controller: _taskTitleController,
          label: 'Título da Task',
          hint: 'Ex: Organização do evento',
          maxLength: 100,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Título é obrigatório';
            }
            if (value.trim().length < 3) {
              return 'Título deve ter pelo menos 3 caracteres';
            }
            return null;
          },
        ),

        const SizedBox(height: AppDimensions.spacingMd),

        // Descrição
        CustomTextField(
          controller: _taskDescriptionController,
          label: 'Descrição',
          hint: 'Descreva o objetivo desta task...',
          maxLines: 4,
          maxLength: 500,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Descrição é obrigatória';
            }
            return null;
          },
        ),

        const SizedBox(height: AppDimensions.spacingMd),

        // Prioridade
        _buildPrioritySelector(
          'Prioridade da Task',
          _taskPriority,
          (priority) => setState(() => _taskPriority = priority),
        ),

        const SizedBox(height: AppDimensions.spacingXl),

        // Botão de criar
        SizedBox(
          width: double.infinity,
          child: CustomButton(
            text: 'Criar Task',
            onPressed: taskController.isLoading
                ? null
                : () => _handleCreateTask(taskController),
            isLoading: taskController.isLoading,
          ),
        ),
      ],
    );
  }

  /// Constrói o formulário para criar Microtask
  Widget _buildMicrotaskForm(TaskController taskController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Seleção de Task pai
        _buildTaskSelector(taskController),

        const SizedBox(height: AppDimensions.spacingMd),

        // Título
        CustomTextField(
          controller: _microtaskTitleController,
          label: 'Título da Microtask',
          hint: 'Ex: Configurar som e iluminação',
          maxLength: 100,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Título é obrigatório';
            }
            if (value.trim().length < 3) {
              return 'Título deve ter pelo menos 3 caracteres';
            }
            return null;
          },
        ),

        const SizedBox(height: AppDimensions.spacingMd),

        // Descrição
        CustomTextField(
          controller: _microtaskDescriptionController,
          label: 'Descrição',
          hint: 'Descreva o que precisa ser feito...',
          maxLines: 4,
          maxLength: 500,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Descrição é obrigatória';
            }
            return null;
          },
        ),

        const SizedBox(height: AppDimensions.spacingMd),

        // Horas estimadas e máximo de voluntários
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _estimatedHoursController,
                label: 'Horas Estimadas',
                hint: '1',
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Campo obrigatório';
                  }
                  final hours = int.tryParse(value);
                  if (hours == null || hours <= 0) {
                    return 'Deve ser um número maior que 0';
                  }
                  if (hours > 168) {
                    return 'Máximo 168 horas';
                  }
                  return null;
                },
              ),
            ),

            const SizedBox(width: AppDimensions.spacingMd),

            Expanded(
              child: CustomTextField(
                controller: _maxVolunteersController,
                label: 'Máx. Voluntários',
                hint: '1',
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Campo obrigatório';
                  }
                  final volunteers = int.tryParse(value);
                  if (volunteers == null || volunteers <= 0) {
                    return 'Deve ser um número maior que 0';
                  }
                  if (volunteers > 50) {
                    return 'Máximo 50 voluntários';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: AppDimensions.spacingMd),

        // Prioridade
        _buildPrioritySelector(
          'Prioridade da Microtask',
          _microtaskPriority,
          (priority) => setState(() => _microtaskPriority = priority),
        ),

        const SizedBox(height: AppDimensions.spacingMd),

        // Habilidades necessárias
        _buildSkillsSelector(),

        const SizedBox(height: AppDimensions.spacingMd),

        // Recursos necessários
        _buildResourcesSelector(),

        const SizedBox(height: AppDimensions.spacingMd),

        // Notas adicionais
        CustomTextField(
          controller: _microtaskNotesController,
          label: 'Notas Adicionais (Opcional)',
          hint: 'Informações extras para os voluntários...',
          maxLines: 3,
          maxLength: 300,
        ),

        const SizedBox(height: AppDimensions.spacingXl),

        // Botão de criar
        SizedBox(
          width: double.infinity,
          child: CustomButton(
            text: 'Criar Microtask',
            onPressed: taskController.isLoading
                ? null
                : () => _handleCreateMicrotask(taskController),
            isLoading: taskController.isLoading,
          ),
        ),
      ],
    );
  }

  /// Constrói o seletor de prioridade
  Widget _buildPrioritySelector(
    String label,
    Priority currentPriority,
    Function(Priority) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeMd,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),

        const SizedBox(height: AppDimensions.spacingSm),

        Row(
          children: Priority.values.map((priority) {
            final isSelected = priority == currentPriority;
            Color backgroundColor;
            Color textColor;

            switch (priority) {
              case Priority.high:
                backgroundColor = isSelected
                    ? AppColors.error
                    : AppColors.error.withValues(alpha: 0.1);
                textColor = isSelected
                    ? AppColors.textOnPrimary
                    : AppColors.error;
                break;
              case Priority.medium:
                backgroundColor = isSelected
                    ? AppColors.warning
                    : AppColors.warning.withValues(alpha: 0.1);
                textColor = isSelected
                    ? AppColors.textOnPrimary
                    : AppColors.warning;
                break;
              case Priority.low:
                backgroundColor = isSelected
                    ? AppColors.success
                    : AppColors.success.withValues(alpha: 0.1);
                textColor = isSelected
                    ? AppColors.textOnPrimary
                    : AppColors.success;
                break;
            }

            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right: priority != Priority.values.last
                      ? AppDimensions.spacingSm
                      : 0,
                ),
                child: GestureDetector(
                  onTap: () => onChanged(priority),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingMd,
                    ),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusMd,
                      ),
                      border: Border.all(
                        color: isSelected
                            ? backgroundColor
                            : backgroundColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      priority.displayName,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: AppDimensions.fontSizeMd,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói o seletor de Task pai para Microtask
  Widget _buildTaskSelector(TaskController taskController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Task Pai',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeMd,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),

        const SizedBox(height: AppDimensions.spacingSm),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMd,
            vertical: AppDimensions.paddingSm,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedTaskId,
              hint: const Text('Selecione uma Task'),
              isExpanded: true,
              items: taskController.tasks.map((task) {
                return DropdownMenuItem<String>(
                  value: task.id,
                  child: Text(task.title, overflow: TextOverflow.ellipsis),
                );
              }).toList(),
              onChanged: (taskId) {
                setState(() => _selectedTaskId = taskId);
              },
            ),
          ),
        ),

        if (_selectedTaskId == null) ...[
          const SizedBox(height: AppDimensions.spacingSm),
          const Text(
            'Você precisa criar uma Task primeiro antes de criar Microtasks.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeSm,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  /// Constrói o seletor de habilidades
  Widget _buildSkillsSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Habilidades Necessárias',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeMd,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),

        const SizedBox(height: AppDimensions.spacingSm),

        if (_currentEvent!.requiredSkills.isEmpty) ...[
          const Text(
            'Nenhuma habilidade definida no evento.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeSm,
              color: AppColors.textSecondary,
            ),
          ),
        ] else ...[
          Wrap(
            spacing: AppDimensions.spacingSm,
            runSpacing: AppDimensions.spacingSm,
            children: _currentEvent!.requiredSkills.map((skill) {
              final isSelected = _selectedSkills.contains(skill);

              return SkillChip(
                label: skill,
                isSelected: isSelected,
                onTap: () => _toggleSkill(skill),
                backgroundColor: isSelected
                    ? AppColors.primary
                    : AppColors.primary.withValues(alpha: 0.1),
                textColor: isSelected
                    ? AppColors.textOnPrimary
                    : AppColors.primary,
                borderColor: AppColors.primary.withValues(alpha: 0.3),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  /// Constrói o seletor de recursos
  Widget _buildResourcesSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recursos Necessários',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeMd,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),

        const SizedBox(height: AppDimensions.spacingSm),

        if (_currentEvent!.requiredResources.isEmpty) ...[
          const Text(
            'Nenhum recurso definido no evento.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeSm,
              color: AppColors.textSecondary,
            ),
          ),
        ] else ...[
          Wrap(
            spacing: AppDimensions.spacingSm,
            runSpacing: AppDimensions.spacingSm,
            children: _currentEvent!.requiredResources.map((resource) {
              final isSelected = _selectedResources.contains(resource);

              return SkillChip(
                label: resource,
                isSelected: isSelected,
                onTap: () => _toggleResource(resource),
                backgroundColor: isSelected
                    ? AppColors.secondary
                    : AppColors.secondary.withValues(alpha: 0.1),
                textColor: isSelected
                    ? AppColors.textOnPrimary
                    : AppColors.secondary,
                borderColor: AppColors.secondary.withValues(alpha: 0.3),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  /// Alterna seleção de habilidade
  void _toggleSkill(String skill) {
    setState(() {
      if (_selectedSkills.contains(skill)) {
        _selectedSkills.remove(skill);
      } else {
        _selectedSkills.add(skill);
      }
    });
  }

  /// Alterna seleção de recurso
  void _toggleResource(String resource) {
    setState(() {
      if (_selectedResources.contains(resource)) {
        _selectedResources.remove(resource);
      } else {
        _selectedResources.add(resource);
      }
    });
  }

  /// Manipula criação de Task
  Future<void> _handleCreateTask(TaskController taskController) async {
    final currentUserId = context.read<AuthController>().currentUser?.id;
    if (currentUserId == null) return;

    final success = await taskController.createTask(
      eventId: widget.eventId,
      title: _taskTitleController.text.trim(),
      description: _taskDescriptionController.text.trim(),
      priority: _taskPriority,
      createdBy: currentUserId,
    );

    if (success && mounted) {
      _showSuccessSnackBar('Task criada com sucesso!');
      _clearTaskForm();
    } else if (mounted && taskController.hasError) {
      _showErrorSnackBar(taskController.errorMessage ?? 'Erro ao criar task');
    }
  }

  /// Manipula criação de Microtask
  Future<void> _handleCreateMicrotask(TaskController taskController) async {
    if (_selectedTaskId == null) {
      _showErrorSnackBar('Selecione uma Task pai primeiro');
      return;
    }

    final currentUserId = context.read<AuthController>().currentUser?.id;
    if (currentUserId == null) return;

    final estimatedHours =
        int.tryParse(_estimatedHoursController.text.trim()) ?? 1;
    final maxVolunteers =
        int.tryParse(_maxVolunteersController.text.trim()) ?? 1;

    final success = await taskController.createMicrotask(
      taskId: _selectedTaskId!,
      eventId: widget.eventId,
      title: _microtaskTitleController.text.trim(),
      description: _microtaskDescriptionController.text.trim(),
      requiredSkills: _selectedSkills,
      requiredResources: _selectedResources,
      estimatedHours: estimatedHours,
      priority: _microtaskPriority,
      maxVolunteers: maxVolunteers,
      createdBy: currentUserId,
      notes: _microtaskNotesController.text.trim(),
    );

    if (success && mounted) {
      _showSuccessSnackBar('Microtask criada com sucesso!');
      _clearMicrotaskForm();
    } else if (mounted && taskController.hasError) {
      _showErrorSnackBar(
        taskController.errorMessage ?? 'Erro ao criar microtask',
      );
    }
  }

  /// Limpa o formulário de Task
  void _clearTaskForm() {
    _taskTitleController.clear();
    _taskDescriptionController.clear();
    setState(() => _taskPriority = Priority.medium);
  }

  /// Limpa o formulário de Microtask
  void _clearMicrotaskForm() {
    _microtaskTitleController.clear();
    _microtaskDescriptionController.clear();
    _microtaskNotesController.clear();
    _estimatedHoursController.text = '1';
    _maxVolunteersController.text = '1';
    setState(() {
      _microtaskPriority = Priority.medium;
      _selectedTaskId = null;
      _selectedSkills.clear();
      _selectedResources.clear();
    });
  }

  /// Mostra snackbar de sucesso
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  /// Mostra snackbar de erro
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _taskTitleController.dispose();
    _taskDescriptionController.dispose();
    _microtaskTitleController.dispose();
    _microtaskDescriptionController.dispose();
    _microtaskNotesController.dispose();
    _estimatedHoursController.dispose();
    _maxVolunteersController.dispose();
    super.dispose();
  }
}
