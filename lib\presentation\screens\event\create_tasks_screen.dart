import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../controllers/task_controller.dart';

/// Tela para criar Tasks e Microtasks
/// Disponível apenas para gerenciadores de eventos
class CreateTasksScreen extends StatefulWidget {
  final String eventId;

  const CreateTasksScreen({
    super.key,
    required this.eventId,
  });

  @override
  State<CreateTasksScreen> createState() => _CreateTasksScreenState();
}

class _CreateTasksScreenState extends State<CreateTasksScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<TaskController>(
      builder: (context, taskController, child) {
        return Scaffold(
          backgroundColor: AppColors.background,
          body: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_task,
                  size: 64,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: AppDimensions.spacingMd),
                Text(
                  'Criar Tasks',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSizeLg,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingSm),
                Text(
                  'Funcionalidade em desenvolvimento',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSizeMd,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
