import '../models/task_model.dart';
import '../models/microtask_model.dart';
import '../models/user_microtask_model.dart';
import '../models/volunteer_profile_model.dart';
import '../services/task_service.dart';
import '../services/assignment_service.dart';
import '../../core/constants/enums.dart';
import '../../core/exceptions/app_exceptions.dart';

/// Repositório responsável por abstrair as operações relacionadas a tasks e microtasks
/// Combina TaskService e AssignmentService para fornecer uma interface unificada
class TaskRepository {
  final TaskService _taskService = TaskService();
  final AssignmentService _assignmentService = AssignmentService();

  // ========== OPERAÇÕES DE TASK ==========

  /// Cria uma nova task
  Future<TaskModel> createTask(TaskModel task) async {
    try {
      return await _taskService.createTask(task);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao criar task: ${e.toString()}');
    }
  }

  /// Busca todas as tasks de um evento
  Future<List<TaskModel>> getEventTasks(String eventId) async {
    try {
      return await _taskService.getTasks(eventId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar tasks: ${e.toString()}');
    }
  }

  /// Busca uma task por ID
  Future<TaskModel?> getTaskById(String taskId) async {
    try {
      return await _taskService.getTaskById(taskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar task: ${e.toString()}');
    }
  }

  /// Atualiza uma task
  Future<TaskModel> updateTask(TaskModel task) async {
    try {
      return await _taskService.updateTask(task);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao atualizar task: ${e.toString()}');
    }
  }

  /// Deleta uma task e todas as suas microtasks
  Future<void> deleteTask(String taskId) async {
    try {
      await _taskService.deleteTask(taskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao deletar task: ${e.toString()}');
    }
  }

  // ========== OPERAÇÕES DE MICROTASK ==========

  /// Cria uma nova microtask
  Future<MicrotaskModel> createMicrotask(MicrotaskModel microtask) async {
    try {
      return await _taskService.createMicrotask(microtask);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao criar microtask: ${e.toString()}');
    }
  }

  /// Busca todas as microtasks de uma task
  Future<List<MicrotaskModel>> getTaskMicrotasks(String taskId) async {
    try {
      return await _taskService.getMicrotasks(taskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar microtasks: ${e.toString()}');
    }
  }

  /// Busca todas as microtasks de um evento
  Future<List<MicrotaskModel>> getEventMicrotasks(String eventId) async {
    try {
      return await _taskService.getEventMicrotasks(eventId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar microtasks do evento: ${e.toString()}');
    }
  }

  /// Busca uma microtask por ID
  Future<MicrotaskModel?> getMicrotaskById(String microtaskId) async {
    try {
      return await _taskService.getMicrotaskById(microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar microtask: ${e.toString()}');
    }
  }

  /// Atualiza uma microtask
  Future<MicrotaskModel> updateMicrotask(MicrotaskModel microtask) async {
    try {
      return await _taskService.updateMicrotask(microtask);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao atualizar microtask: ${e.toString()}');
    }
  }

  /// Deleta uma microtask
  Future<void> deleteMicrotask(String microtaskId) async {
    try {
      await _taskService.deleteMicrotask(microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao deletar microtask: ${e.toString()}');
    }
  }

  /// Atualiza o status de uma microtask para um usuário específico
  Future<void> updateUserMicrotaskStatus(
    String microtaskId, 
    String userId, 
    UserMicrotaskStatus status
  ) async {
    try {
      await _taskService.updateMicrotaskStatus(microtaskId, userId, status);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao atualizar status: ${e.toString()}');
    }
  }

  // ========== OPERAÇÕES DE ATRIBUIÇÃO ==========

  /// Atribui um voluntário a uma microtask
  Future<UserMicrotaskModel> assignVolunteerToMicrotask(String userId, String microtaskId) async {
    try {
      return await _assignmentService.assignVolunteerToMicrotask(userId, microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao atribuir voluntário: ${e.toString()}');
    }
  }

  /// Remove um voluntário de uma microtask
  Future<void> removeVolunteerFromMicrotask(String userId, String microtaskId) async {
    try {
      await _assignmentService.removeVolunteerFromMicrotask(userId, microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao remover voluntário: ${e.toString()}');
    }
  }

  /// Busca voluntários compatíveis com uma microtask
  Future<List<VolunteerProfileModel>> getCompatibleVolunteers(String microtaskId) async {
    try {
      return await _assignmentService.getCompatibleVolunteers(microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar voluntários compatíveis: ${e.toString()}');
    }
  }

  /// Busca voluntários atribuídos a uma microtask
  Future<List<VolunteerProfileModel>> getAssignedVolunteers(String microtaskId) async {
    try {
      return await _assignmentService.getAssignedVolunteers(microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar voluntários atribuídos: ${e.toString()}');
    }
  }

  /// Verifica se um voluntário pode ser atribuído a uma microtask
  Future<bool> canAssignVolunteer(String userId, String microtaskId) async {
    try {
      return await _assignmentService.canAssignVolunteer(userId, microtaskId);
    } catch (e) {
      return false;
    }
  }

  // ========== OPERAÇÕES DE CONSULTA ==========

  /// Busca as relações usuário-microtask para uma microtask específica
  Future<List<UserMicrotaskModel>> getUserMicrotasks(String microtaskId) async {
    try {
      return await _taskService.getUserMicrotasks(microtaskId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar relações usuário-microtask: ${e.toString()}');
    }
  }

  /// Busca as microtasks atribuídas a um usuário específico
  Future<List<UserMicrotaskModel>> getUserAssignedMicrotasks(String userId, String eventId) async {
    try {
      return await _taskService.getUserAssignedMicrotasks(userId, eventId);
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar microtasks do usuário: ${e.toString()}');
    }
  }

  /// Busca tasks e microtasks de um evento de forma hierárquica
  Future<Map<TaskModel, List<MicrotaskModel>>> getEventTasksHierarchy(String eventId) async {
    try {
      final tasks = await getEventTasks(eventId);
      final hierarchy = <TaskModel, List<MicrotaskModel>>{};

      for (final task in tasks) {
        final microtasks = await getTaskMicrotasks(task.id);
        hierarchy[task] = microtasks;
      }

      return hierarchy;
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar hierarquia: ${e.toString()}');
    }
  }

  /// Busca estatísticas de progresso de um evento
  Future<Map<String, dynamic>> getEventProgress(String eventId) async {
    try {
      final tasks = await getEventTasks(eventId);
      final microtasks = await getEventMicrotasks(eventId);

      final totalTasks = tasks.length;
      final completedTasks = tasks.where((t) => t.isCompleted).length;
      final totalMicrotasks = microtasks.length;
      final completedMicrotasks = microtasks.where((m) => m.isCompleted).length;
      final inProgressMicrotasks = microtasks.where((m) => m.isInProgress).length;
      final assignedMicrotasks = microtasks.where((m) => m.isAssigned).length;

      return {
        'totalTasks': totalTasks,
        'completedTasks': completedTasks,
        'totalMicrotasks': totalMicrotasks,
        'completedMicrotasks': completedMicrotasks,
        'inProgressMicrotasks': inProgressMicrotasks,
        'assignedMicrotasks': assignedMicrotasks,
        'taskProgress': totalTasks > 0 ? completedTasks / totalTasks : 0.0,
        'microtaskProgress': totalMicrotasks > 0 ? completedMicrotasks / totalMicrotasks : 0.0,
      };
    } catch (e) {
      if (e is AppException) rethrow;
      throw RepositoryException('Erro no repositório ao buscar progresso: ${e.toString()}');
    }
  }
}
