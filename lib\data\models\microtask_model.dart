import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/constants/enums.dart';

/// Modelo de dados para representar uma Microtask no sistema
/// Microtasks são as ações executáveis que podem ter múltiplos voluntários atribuídos
/// Baseado na estrutura definida no SPEC_GERAL.md
class MicrotaskModel {
  final String id;
  final String taskId;
  final String eventId;
  final String title;
  final String description;
  final List<String> assignedTo;
  final int maxVolunteers;
  final List<String> requiredSkills;
  final List<String> requiredResources;
  final int estimatedHours;
  final Priority priority;
  final MicrotaskStatus status;
  final String createdBy;
  final DateTime? assignedAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MicrotaskModel({
    required this.id,
    required this.taskId,
    required this.eventId,
    required this.title,
    required this.description,
    required this.assignedTo,
    required this.maxVolunteers,
    required this.requiredSkills,
    required this.requiredResources,
    required this.estimatedHours,
    required this.priority,
    required this.status,
    required this.createdBy,
    this.assignedAt,
    this.startedAt,
    this.completedAt,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Cria uma cópia da microtask com campos atualizados
  MicrotaskModel copyWith({
    String? id,
    String? taskId,
    String? eventId,
    String? title,
    String? description,
    List<String>? assignedTo,
    int? maxVolunteers,
    List<String>? requiredSkills,
    List<String>? requiredResources,
    int? estimatedHours,
    Priority? priority,
    MicrotaskStatus? status,
    String? createdBy,
    DateTime? assignedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MicrotaskModel(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      eventId: eventId ?? this.eventId,
      title: title ?? this.title,
      description: description ?? this.description,
      assignedTo: assignedTo ?? this.assignedTo,
      maxVolunteers: maxVolunteers ?? this.maxVolunteers,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      requiredResources: requiredResources ?? this.requiredResources,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      assignedAt: assignedAt ?? this.assignedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Converte o modelo para Map para salvar no Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'taskId': taskId,
      'eventId': eventId,
      'title': title,
      'description': description,
      'assignedTo': assignedTo,
      'maxVolunteers': maxVolunteers,
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'estimatedHours': estimatedHours,
      'priority': priority.value,
      'status': status.value,
      'createdBy': createdBy,
      'assignedAt': assignedAt != null ? Timestamp.fromDate(assignedAt!) : null,
      'startedAt': startedAt != null ? Timestamp.fromDate(startedAt!) : null,
      'completedAt': completedAt != null
          ? Timestamp.fromDate(completedAt!)
          : null,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// Cria uma instância a partir de um documento do Firestore
  factory MicrotaskModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return MicrotaskModel(
      id: doc.id,
      taskId: data['taskId'] ?? '',
      eventId: data['eventId'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      assignedTo: List<String>.from(data['assignedTo'] ?? []),
      maxVolunteers: data['maxVolunteers'] ?? 1,
      requiredSkills: List<String>.from(data['requiredSkills'] ?? []),
      requiredResources: List<String>.from(data['requiredResources'] ?? []),
      estimatedHours: data['estimatedHours'] ?? 1,
      priority: Priority.fromString(data['priority'] ?? 'medium'),
      status: MicrotaskStatus.fromString(data['status'] ?? 'pending'),
      createdBy: data['createdBy'] ?? '',
      assignedAt: (data['assignedAt'] as Timestamp?)?.toDate(),
      startedAt: (data['startedAt'] as Timestamp?)?.toDate(),
      completedAt: (data['completedAt'] as Timestamp?)?.toDate(),
      notes: data['notes'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  /// Cria uma instância a partir de um Map
  factory MicrotaskModel.fromMap(Map<String, dynamic> map) {
    return MicrotaskModel(
      id: map['id'] ?? '',
      taskId: map['taskId'] ?? '',
      eventId: map['eventId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      assignedTo: List<String>.from(map['assignedTo'] ?? []),
      maxVolunteers: map['maxVolunteers'] ?? 1,
      requiredSkills: List<String>.from(map['requiredSkills'] ?? []),
      requiredResources: List<String>.from(map['requiredResources'] ?? []),
      estimatedHours: map['estimatedHours'] ?? 1,
      priority: Priority.fromString(map['priority'] ?? 'medium'),
      status: MicrotaskStatus.fromString(map['status'] ?? 'pending'),
      createdBy: map['createdBy'] ?? '',
      assignedAt: map['assignedAt'] is DateTime ? map['assignedAt'] : null,
      startedAt: map['startedAt'] is DateTime ? map['startedAt'] : null,
      completedAt: map['completedAt'] is DateTime ? map['completedAt'] : null,
      notes: map['notes'] ?? '',
      createdAt: map['createdAt'] is DateTime
          ? map['createdAt']
          : DateTime.now(),
      updatedAt: map['updatedAt'] is DateTime
          ? map['updatedAt']
          : DateTime.now(),
    );
  }

  /// Converte para Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'taskId': taskId,
      'eventId': eventId,
      'title': title,
      'description': description,
      'assignedTo': assignedTo,
      'maxVolunteers': maxVolunteers,
      'requiredSkills': requiredSkills,
      'requiredResources': requiredResources,
      'estimatedHours': estimatedHours,
      'priority': priority.value,
      'status': status.value,
      'createdBy': createdBy,
      'assignedAt': assignedAt,
      'startedAt': startedAt,
      'completedAt': completedAt,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// Factory para criar uma nova microtask
  factory MicrotaskModel.create({
    required String taskId,
    required String eventId,
    required String title,
    required String description,
    required List<String> requiredSkills,
    required List<String> requiredResources,
    required int estimatedHours,
    required Priority priority,
    required int maxVolunteers,
    required String createdBy,
    String notes = '',
  }) {
    final now = DateTime.now();

    return MicrotaskModel(
      id: '', // Será definido pelo Firestore
      taskId: taskId,
      eventId: eventId,
      title: title,
      description: description,
      assignedTo: [],
      maxVolunteers: maxVolunteers,
      requiredSkills: requiredSkills,
      requiredResources: requiredResources,
      estimatedHours: estimatedHours,
      priority: priority,
      status: MicrotaskStatus.pending,
      createdBy: createdBy,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Verifica se a microtask pode receber mais voluntários
  bool get canAssignMoreVolunteers => assignedTo.length < maxVolunteers;

  /// Retorna o número de vagas disponíveis
  int get availableSlots => maxVolunteers - assignedTo.length;

  /// Verifica se um usuário está atribuído à microtask
  bool isUserAssigned(String userId) => assignedTo.contains(userId);

  /// Verifica se a microtask está completa (atingiu o limite de voluntários)
  bool get isFull => assignedTo.length >= maxVolunteers;

  /// Verifica se a microtask está concluída
  bool get isCompleted => status == MicrotaskStatus.completed;

  /// Verifica se a microtask está em progresso
  bool get isInProgress => status == MicrotaskStatus.inProgress;

  /// Verifica se a microtask está pendente
  bool get isPending => status == MicrotaskStatus.pending;

  /// Verifica se a microtask foi atribuída
  bool get isAssigned => status == MicrotaskStatus.assigned;

  /// Verifica se a microtask foi cancelada
  bool get isCancelled => status == MicrotaskStatus.cancelled;

  /// Valida os dados da microtask
  List<String> validate() {
    final errors = <String>[];

    if (title.trim().isEmpty) {
      errors.add('Título é obrigatório');
    }

    if (title.trim().length < 3) {
      errors.add('Título deve ter pelo menos 3 caracteres');
    }

    if (title.trim().length > 100) {
      errors.add('Título deve ter no máximo 100 caracteres');
    }

    if (description.trim().isEmpty) {
      errors.add('Descrição é obrigatória');
    }

    if (description.trim().length > 500) {
      errors.add('Descrição deve ter no máximo 500 caracteres');
    }

    if (taskId.trim().isEmpty) {
      errors.add('ID da task é obrigatório');
    }

    if (eventId.trim().isEmpty) {
      errors.add('ID do evento é obrigatório');
    }

    if (createdBy.trim().isEmpty) {
      errors.add('Criador é obrigatório');
    }

    if (maxVolunteers <= 0) {
      errors.add('Número máximo de voluntários deve ser maior que zero');
    }

    if (maxVolunteers > 50) {
      errors.add('Número máximo de voluntários não pode exceder 50');
    }

    if (estimatedHours <= 0) {
      errors.add('Horas estimadas devem ser maior que zero');
    }

    if (estimatedHours > 168) {
      // 1 semana
      errors.add('Horas estimadas não podem exceder 168 horas');
    }

    if (assignedTo.length > maxVolunteers) {
      errors.add('Número de voluntários atribuídos excede o máximo permitido');
    }

    return errors;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MicrotaskModel &&
        other.id == id &&
        other.taskId == taskId &&
        other.eventId == eventId &&
        other.title == title &&
        other.description == description &&
        other.maxVolunteers == maxVolunteers &&
        other.priority == priority &&
        other.status == status &&
        other.createdBy == createdBy;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      taskId,
      eventId,
      title,
      description,
      maxVolunteers,
      priority,
      status,
      createdBy,
    );
  }

  @override
  String toString() {
    return 'MicrotaskModel(id: $id, title: $title, status: ${status.displayName}, assigned: ${assignedTo.length}/$maxVolunteers)';
  }
}
