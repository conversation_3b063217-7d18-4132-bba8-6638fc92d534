import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/constants/enums.dart';

/// Modelo de dados para representar uma Task no sistema
/// Tasks servem como agrupadores organizacionais para Microtasks
/// Baseado na estrutura definida no SPEC_GERAL.md
class TaskModel {
  final String id;
  final String eventId;
  final String title;
  final String description;
  final Priority priority;
  final TaskStatus status;
  final String createdBy;
  final int microtaskCount;
  final int completedMicrotasks;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TaskModel({
    required this.id,
    required this.eventId,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.createdBy,
    required this.microtaskCount,
    required this.completedMicrotasks,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Cria uma cópia da task com campos atualizados
  TaskModel copyWith({
    String? id,
    String? eventId,
    String? title,
    String? description,
    Priority? priority,
    TaskStatus? status,
    String? createdBy,
    int? microtaskCount,
    int? completedMicrotasks,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaskModel(
      id: id ?? this.id,
      eventId: eventId ?? this.eventId,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      microtaskCount: microtaskCount ?? this.microtaskCount,
      completedMicrotasks: completedMicrotasks ?? this.completedMicrotasks,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Converte o modelo para Map para salvar no Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'eventId': eventId,
      'title': title,
      'description': description,
      'priority': priority.value,
      'status': status.value,
      'createdBy': createdBy,
      'microtaskCount': microtaskCount,
      'completedMicrotasks': completedMicrotasks,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// Cria uma instância a partir de um documento do Firestore
  factory TaskModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return TaskModel(
      id: doc.id,
      eventId: data['eventId'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      priority: Priority.fromString(data['priority'] ?? 'medium'),
      status: TaskStatus.fromString(data['status'] ?? 'pending'),
      createdBy: data['createdBy'] ?? '',
      microtaskCount: data['microtaskCount'] ?? 0,
      completedMicrotasks: data['completedMicrotasks'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  /// Cria uma instância a partir de um Map
  factory TaskModel.fromMap(Map<String, dynamic> map) {
    return TaskModel(
      id: map['id'] ?? '',
      eventId: map['eventId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      priority: Priority.fromString(map['priority'] ?? 'medium'),
      status: TaskStatus.fromString(map['status'] ?? 'pending'),
      createdBy: map['createdBy'] ?? '',
      microtaskCount: map['microtaskCount'] ?? 0,
      completedMicrotasks: map['completedMicrotasks'] ?? 0,
      createdAt: map['createdAt'] is DateTime 
          ? map['createdAt'] 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is DateTime 
          ? map['updatedAt'] 
          : DateTime.now(),
    );
  }

  /// Converte para Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'eventId': eventId,
      'title': title,
      'description': description,
      'priority': priority.value,
      'status': status.value,
      'createdBy': createdBy,
      'microtaskCount': microtaskCount,
      'completedMicrotasks': completedMicrotasks,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// Factory para criar uma nova task
  factory TaskModel.create({
    required String eventId,
    required String title,
    required String description,
    required Priority priority,
    required String createdBy,
  }) {
    final now = DateTime.now();
    
    return TaskModel(
      id: '', // Será definido pelo Firestore
      eventId: eventId,
      title: title,
      description: description,
      priority: priority,
      status: TaskStatus.pending,
      createdBy: createdBy,
      microtaskCount: 0,
      completedMicrotasks: 0,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Calcula o progresso da task baseado nas microtasks concluídas
  double get progress {
    if (microtaskCount == 0) return 0.0;
    return completedMicrotasks / microtaskCount;
  }

  /// Verifica se a task está concluída
  bool get isCompleted => status == TaskStatus.completed;

  /// Verifica se a task está em progresso
  bool get isInProgress => status == TaskStatus.inProgress;

  /// Verifica se a task está pendente
  bool get isPending => status == TaskStatus.pending;

  /// Valida os dados da task
  List<String> validate() {
    final errors = <String>[];
    
    if (title.trim().isEmpty) {
      errors.add('Título é obrigatório');
    }
    
    if (title.trim().length < 3) {
      errors.add('Título deve ter pelo menos 3 caracteres');
    }
    
    if (title.trim().length > 100) {
      errors.add('Título deve ter no máximo 100 caracteres');
    }
    
    if (description.trim().isEmpty) {
      errors.add('Descrição é obrigatória');
    }
    
    if (description.trim().length > 500) {
      errors.add('Descrição deve ter no máximo 500 caracteres');
    }
    
    if (eventId.trim().isEmpty) {
      errors.add('ID do evento é obrigatório');
    }
    
    if (createdBy.trim().isEmpty) {
      errors.add('Criador é obrigatório');
    }
    
    if (microtaskCount < 0) {
      errors.add('Número de microtasks não pode ser negativo');
    }
    
    if (completedMicrotasks < 0) {
      errors.add('Número de microtasks concluídas não pode ser negativo');
    }
    
    if (completedMicrotasks > microtaskCount) {
      errors.add('Número de microtasks concluídas não pode ser maior que o total');
    }
    
    return errors;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is TaskModel &&
        other.id == id &&
        other.eventId == eventId &&
        other.title == title &&
        other.description == description &&
        other.priority == priority &&
        other.status == status &&
        other.createdBy == createdBy &&
        other.microtaskCount == microtaskCount &&
        other.completedMicrotasks == completedMicrotasks;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      eventId,
      title,
      description,
      priority,
      status,
      createdBy,
      microtaskCount,
      completedMicrotasks,
    );
  }

  @override
  String toString() {
    return 'TaskModel(id: $id, title: $title, status: ${status.displayName}, progress: ${(progress * 100).toStringAsFixed(1)}%)';
  }
}
