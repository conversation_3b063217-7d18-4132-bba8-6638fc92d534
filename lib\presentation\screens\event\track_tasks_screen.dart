import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/task_model.dart';
import '../../../data/models/microtask_model.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/volunteer_controller.dart';
import '../../widgets/common/loading_widget.dart';

/// Tela para acompanhar o progresso das Tasks e Microtasks
/// Disponível para todos os participantes do evento
class TrackTasksScreen extends StatefulWidget {
  final String eventId;

  const TrackTasksScreen({super.key, required this.eventId});

  @override
  State<TrackTasksScreen> createState() => _TrackTasksScreenState();
}

class _TrackTasksScreenState extends State<TrackTasksScreen> {
  bool _isLoading = true;
  String? _currentUserId;
  UserRole _userRole = UserRole.none;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Carrega os dados das tasks e determina o papel do usuário
  Future<void> _loadData() async {
    final taskController = context.read<TaskController>();
    final authController = context.read<AuthController>();

    setState(() => _isLoading = true);

    try {
      _currentUserId = authController.currentUser?.id;

      // Carrega a hierarquia de tasks e microtasks
      await taskController.loadEventTasksHierarchy(widget.eventId);

      // Carrega o progresso do evento
      await taskController.loadEventProgress(widget.eventId);

      // TODO: Determinar papel do usuário no evento
      // Por enquanto, assumindo que é voluntário
      _userRole = UserRole.volunteer;
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Erro ao carregar dados: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<TaskController, VolunteerController>(
      builder: (context, taskController, volunteerController, child) {
        if (_isLoading) {
          return const LoadingWidget(message: 'Carregando tasks...');
        }

        if (taskController.hasError) {
          return _buildErrorState(taskController);
        }

        if (taskController.tasksHierarchy.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: _loadData,
          child: Column(
            children: [
              // Header com estatísticas
              _buildProgressHeader(taskController),

              // Lista de tasks hierárquica
              Expanded(
                child: _buildTasksList(taskController, volunteerController),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Constrói o header com estatísticas de progresso
  Widget _buildProgressHeader(TaskController taskController) {
    final progress = taskController.eventProgress;

    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMd),
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Progresso do Evento',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingMd),

          // Estatísticas em grid
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Tasks',
                  '${progress['completedTasks'] ?? 0}/${progress['totalTasks'] ?? 0}',
                  progress['taskProgress'] ?? 0.0,
                  Icons.folder_outlined,
                  AppColors.primary,
                ),
              ),

              const SizedBox(width: AppDimensions.spacingMd),

              Expanded(
                child: _buildStatCard(
                  'Microtasks',
                  '${progress['completedMicrotasks'] ?? 0}/${progress['totalMicrotasks'] ?? 0}',
                  progress['microtaskProgress'] ?? 0.0,
                  Icons.task_outlined,
                  AppColors.secondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Constrói um card de estatística
  Widget _buildStatCard(
    String title,
    String value,
    double progress,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: AppDimensions.spacingSm),
              Text(
                title,
                style: TextStyle(
                  fontSize: AppDimensions.fontSizeMd,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppDimensions.spacingSm),

          Text(
            value,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXl,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingSm),

          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  /// Constrói a lista hierárquica de tasks
  Widget _buildTasksList(
    TaskController taskController,
    VolunteerController volunteerController,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingMd),
      itemCount: taskController.tasksHierarchy.length,
      itemBuilder: (context, index) {
        final task = taskController.tasksHierarchy.keys.elementAt(index);
        final microtasks = taskController.tasksHierarchy[task] ?? [];

        return _buildTaskExpansionTile(
          task,
          microtasks,
          taskController,
          volunteerController,
        );
      },
    );
  }

  /// Constrói um ExpansionTile para uma task com suas microtasks
  Widget _buildTaskExpansionTile(
    TaskModel task,
    List<MicrotaskModel> microtasks,
    TaskController taskController,
    VolunteerController volunteerController,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingMd),
      elevation: AppDimensions.elevationSm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
      ),
      child: ExpansionTile(
        leading: _buildTaskStatusIcon(task.status),
        title: Text(
          task.title,
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeMd,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppDimensions.spacingXs),
            Text(
              task.description,
              style: const TextStyle(
                fontSize: AppDimensions.fontSizeSm,
                color: AppColors.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppDimensions.spacingSm),
            _buildTaskProgressBar(task),
          ],
        ),
        trailing: _buildPriorityChip(task.priority),
        children: [
          if (microtasks.isEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(AppDimensions.paddingMd),
              child: Text(
                'Nenhuma microtask criada ainda.',
                style: TextStyle(
                  fontSize: AppDimensions.fontSizeSm,
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ] else ...[
            ...microtasks.map(
              (microtask) => _buildMicrotaskTile(
                microtask,
                taskController,
                volunteerController,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Constrói um tile para uma microtask
  Widget _buildMicrotaskTile(
    MicrotaskModel microtask,
    TaskController taskController,
    VolunteerController volunteerController,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMd,
        vertical: AppDimensions.spacingXs,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header da microtask
          Row(
            children: [
              _buildMicrotaskStatusIcon(microtask.status),
              const SizedBox(width: AppDimensions.spacingSm),
              Expanded(
                child: Text(
                  microtask.title,
                  style: const TextStyle(
                    fontSize: AppDimensions.fontSizeMd,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              _buildPriorityChip(microtask.priority),
            ],
          ),

          const SizedBox(height: AppDimensions.spacingSm),

          // Descrição
          Text(
            microtask.description,
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeSm,
              color: AppColors.textSecondary,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: AppDimensions.spacingSm),

          // Informações da microtask
          _buildMicrotaskInfo(microtask),

          // Voluntários atribuídos (se houver)
          if (microtask.assignedTo.isNotEmpty) ...[
            const SizedBox(height: AppDimensions.spacingSm),
            _buildAssignedVolunteers(microtask),
          ],
        ],
      ),
    );
  }

  /// Constrói ícone de status para task
  Widget _buildTaskStatusIcon(TaskStatus status) {
    IconData icon;
    Color color;

    switch (status) {
      case TaskStatus.pending:
        icon = Icons.schedule;
        color = AppColors.warning;
        break;
      case TaskStatus.inProgress:
        icon = Icons.play_circle;
        color = AppColors.info;
        break;
      case TaskStatus.completed:
        icon = Icons.check_circle;
        color = AppColors.success;
        break;
    }

    return Icon(icon, color: color, size: 24);
  }

  /// Constrói ícone de status para microtask
  Widget _buildMicrotaskStatusIcon(MicrotaskStatus status) {
    IconData icon;
    Color color;

    switch (status) {
      case MicrotaskStatus.pending:
        icon = Icons.schedule;
        color = AppColors.warning;
        break;
      case MicrotaskStatus.assigned:
        icon = Icons.assignment_ind;
        color = AppColors.info;
        break;
      case MicrotaskStatus.inProgress:
        icon = Icons.play_circle;
        color = AppColors.primary;
        break;
      case MicrotaskStatus.completed:
        icon = Icons.check_circle;
        color = AppColors.success;
        break;
      case MicrotaskStatus.cancelled:
        icon = Icons.cancel;
        color = AppColors.error;
        break;
    }

    return Icon(icon, color: color, size: 20);
  }

  /// Constrói barra de progresso para task
  Widget _buildTaskProgressBar(TaskModel task) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progresso: ${task.completedMicrotasks}/${task.microtaskCount}',
              style: const TextStyle(
                fontSize: AppDimensions.fontSizeXs,
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              '${(task.progress * 100).toStringAsFixed(0)}%',
              style: const TextStyle(
                fontSize: AppDimensions.fontSizeXs,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        LinearProgressIndicator(
          value: task.progress,
          backgroundColor: AppColors.border,
          valueColor: AlwaysStoppedAnimation<Color>(
            task.isCompleted ? AppColors.success : AppColors.primary,
          ),
        ),
      ],
    );
  }

  /// Constrói chip de prioridade
  Widget _buildPriorityChip(Priority priority) {
    Color backgroundColor;
    Color textColor;

    switch (priority) {
      case Priority.high:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        break;
      case Priority.medium:
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        break;
      case Priority.low:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSm,
        vertical: AppDimensions.paddingXs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: textColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        priority.displayName,
        style: TextStyle(
          fontSize: AppDimensions.fontSizeXs,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  /// Constrói informações da microtask
  Widget _buildMicrotaskInfo(MicrotaskModel microtask) {
    return Row(
      children: [
        // Horas estimadas
        Icon(Icons.schedule, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: AppDimensions.spacingXs),
        Text(
          '${microtask.estimatedHours}h',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            color: AppColors.textSecondary,
          ),
        ),

        const SizedBox(width: AppDimensions.spacingMd),

        // Voluntários
        Icon(Icons.people, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: AppDimensions.spacingXs),
        Text(
          '${microtask.assignedTo.length}/${microtask.maxVolunteers}',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            color: AppColors.textSecondary,
          ),
        ),

        const Spacer(),

        // Status
        Text(
          microtask.status.displayName,
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// Constrói lista de voluntários atribuídos
  Widget _buildAssignedVolunteers(MicrotaskModel microtask) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Voluntários:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: microtask.assignedTo.map((userId) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                'Voluntário', // TODO: Buscar nome real do usuário
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  color: AppColors.primary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói estado de erro
  Widget _buildErrorState(TaskController taskController) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppDimensions.spacingMd),
          Text(
            'Erro ao carregar tasks',
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingSm),
          Text(
            taskController.errorMessage ?? 'Erro desconhecido',
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingLg),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Tentar Novamente'),
          ),
        ],
      ),
    );
  }

  /// Constrói estado vazio
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.task_outlined, size: 64, color: AppColors.textSecondary),
          SizedBox(height: AppDimensions.spacingMd),
          Text(
            'Nenhuma task encontrada',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingSm),
          Text(
            'As tasks criadas aparecerão aqui.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Mostra snackbar de erro
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }
}
