import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/volunteer_profile_model.dart';
import '../../../data/models/user_microtask_model.dart';
import '../../../data/models/microtask_model.dart';

/// Widget de card para exibir voluntário na tela de gerenciamento
class VolunteerAssignmentCard extends StatelessWidget {
  final VolunteerProfileModel volunteer;
  final List<UserMicrotaskModel> assignments;
  final List<MicrotaskModel> microtasks;
  final Function(String microtaskId)? onAssignToMicrotask;
  final Function(String microtaskId)? onRemoveFromMicrotask;
  final VoidCallback? onPromoteToManager;
  final bool showActions;
  final bool isCompact;

  const VolunteerAssignmentCard({
    super.key,
    required this.volunteer,
    this.assignments = const [],
    this.microtasks = const [],
    this.onAssignToMicrotask,
    this.onRemoveFromMicrotask,
    this.onPromoteToManager,
    this.showActions = false,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingMd),
      elevation: AppDimensions.elevationSm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header do voluntário
            _buildVolunteerHeader(),
            
            if (!isCompact) ...[
              // Habilidades
              if (volunteer.skills.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.spacingMd),
                _buildSkillsSection(),
              ],
              
              // Recursos
              if (volunteer.resources.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.spacingSm),
                _buildResourcesSection(),
              ],
            ],
            
            // Atribuições atuais
            if (assignments.isNotEmpty) ...[
              const SizedBox(height: AppDimensions.spacingMd),
              _buildAssignmentsSection(),
            ],
            
            // Ações
            if (showActions) ...[
              const SizedBox(height: AppDimensions.spacingMd),
              _buildActionsSection(),
            ],
          ],
        ),
      ),
    );
  }

  /// Constrói o header do voluntário
  Widget _buildVolunteerHeader() {
    return Row(
      children: [
        // Avatar
        CircleAvatar(
          radius: isCompact ? 16 : 24,
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Text(
            volunteer.displayName.isNotEmpty 
                ? volunteer.displayName[0].toUpperCase()
                : 'V',
            style: TextStyle(
              fontSize: isCompact ? AppDimensions.fontSizeSm : AppDimensions.fontSizeMd,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        
        const SizedBox(width: AppDimensions.spacingMd),
        
        // Informações do voluntário
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                volunteer.displayName.isNotEmpty 
                    ? volunteer.displayName 
                    : 'Voluntário',
                style: TextStyle(
                  fontSize: isCompact ? AppDimensions.fontSizeMd : AppDimensions.fontSizeLg,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              if (volunteer.email.isNotEmpty && !isCompact) ...[
                const SizedBox(height: AppDimensions.spacingXs),
                Text(
                  volunteer.email,
                  style: const TextStyle(
                    fontSize: AppDimensions.fontSizeSm,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Estatísticas
        _buildStatsChip(),
      ],
    );
  }

  /// Constrói chip com estatísticas
  Widget _buildStatsChip() {
    final totalAssignments = assignments.length;
    final completedAssignments = assignments.where((a) => a.isCompleted).length;
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSm,
        vertical: AppDimensions.paddingXs,
      ),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$completedAssignments/$totalAssignments',
        style: const TextStyle(
          fontSize: AppDimensions.fontSizeXs,
          fontWeight: FontWeight.w600,
          color: AppColors.info,
        ),
      ),
    );
  }

  /// Constrói seção de habilidades
  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Habilidades:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeSm,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: volunteer.skills.take(5).map((skill) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              ),
              child: Text(
                skill,
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  color: AppColors.primary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói seção de recursos
  Widget _buildResourcesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recursos:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeSm,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: volunteer.resources.take(5).map((resource) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: AppColors.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              ),
              child: Text(
                resource,
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  color: AppColors.secondary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói seção de atribuições
  Widget _buildAssignmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Atribuições Atuais:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeSm,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        ...assignments.take(3).map((assignment) {
          final microtask = microtasks.firstWhere(
            (m) => m.id == assignment.microtaskId,
            orElse: () => MicrotaskModel.create(
              taskId: '',
              eventId: '',
              title: 'Microtask não encontrada',
              description: '',
              requiredSkills: [],
              requiredResources: [],
              estimatedHours: 0,
              priority: Priority.medium,
              maxVolunteers: 1,
              createdBy: '',
            ),
          );
          
          return Container(
            margin: const EdgeInsets.only(bottom: AppDimensions.spacingXs),
            padding: const EdgeInsets.all(AppDimensions.paddingSm),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              border: Border.all(color: AppColors.border),
            ),
            child: Row(
              children: [
                _buildStatusIcon(assignment.status),
                const SizedBox(width: AppDimensions.spacingSm),
                Expanded(
                  child: Text(
                    microtask.title,
                    style: const TextStyle(
                      fontSize: AppDimensions.fontSizeSm,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (onRemoveFromMicrotask != null) ...[
                  IconButton(
                    onPressed: () => onRemoveFromMicrotask!(assignment.microtaskId),
                    icon: const Icon(Icons.remove_circle_outline),
                    color: AppColors.error,
                    iconSize: 16,
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ],
            ),
          );
        }),
        if (assignments.length > 3) ...[
          Text(
            '+${assignments.length - 3} mais',
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeXs,
              color: AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  /// Constrói ícone de status
  Widget _buildStatusIcon(UserMicrotaskStatus status) {
    IconData icon;
    Color color;
    
    switch (status) {
      case UserMicrotaskStatus.assigned:
        icon = Icons.assignment_ind;
        color = AppColors.info;
        break;
      case UserMicrotaskStatus.inProgress:
        icon = Icons.play_circle;
        color = AppColors.primary;
        break;
      case UserMicrotaskStatus.completed:
        icon = Icons.check_circle;
        color = AppColors.success;
        break;
      case UserMicrotaskStatus.cancelled:
        icon = Icons.cancel;
        color = AppColors.error;
        break;
    }
    
    return Icon(icon, color: color, size: 16);
  }

  /// Constrói seção de ações
  Widget _buildActionsSection() {
    return Row(
      children: [
        if (onAssignToMicrotask != null) ...[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => onAssignToMicrotask!(''), // TODO: Implementar seleção de microtask
              icon: const Icon(Icons.add, size: 16),
              label: const Text('Atribuir'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.primary),
              ),
            ),
          ),
        ],
        
        if (onPromoteToManager != null) ...[
          if (onAssignToMicrotask != null) const SizedBox(width: AppDimensions.spacingSm),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onPromoteToManager,
              icon: const Icon(Icons.admin_panel_settings, size: 16),
              label: const Text('Promover'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.secondary,
                side: const BorderSide(color: AppColors.secondary),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
