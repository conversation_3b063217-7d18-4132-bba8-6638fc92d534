import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/task_model.dart';
import '../models/microtask_model.dart';
import '../models/user_microtask_model.dart';
import '../../core/constants/enums.dart';
import '../../core/exceptions/app_exceptions.dart';

/// Serviço responsável por operações relacionadas a tasks e microtasks no Firebase
class TaskService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = const Uuid();

  // Referências das coleções
  CollectionReference get _tasksCollection => _firestore.collection('tasks');
  CollectionReference get _microtasksCollection =>
      _firestore.collection('microtasks');
  CollectionReference get _userMicrotasksCollection =>
      _firestore.collection('user_microtasks');

  /// Cria uma nova task
  Future<TaskModel> createTask(TaskModel task) async {
    try {
      // Gera um ID único para a task
      final taskId = _uuid.v4();
      final taskWithId = task.copyWith(id: taskId);

      // Valida os dados antes de salvar
      final validationErrors = taskWithId.validate();
      if (validationErrors.isNotEmpty) {
        throw ValidationException(
          'Dados inválidos: ${validationErrors.join(', ')}',
        );
      }

      // Salva no Firestore
      await _tasksCollection.doc(taskId).set(taskWithId.toFirestore());

      return taskWithId;
    } catch (e) {
      if (e is ValidationException) rethrow;
      throw DatabaseException('Erro ao criar task: ${e.toString()}');
    }
  }

  /// Cria uma nova microtask
  Future<MicrotaskModel> createMicrotask(MicrotaskModel microtask) async {
    try {
      // Gera um ID único para a microtask
      final microtaskId = _uuid.v4();
      final microtaskWithId = microtask.copyWith(id: microtaskId);

      // Valida os dados antes de salvar
      final validationErrors = microtaskWithId.validate();
      if (validationErrors.isNotEmpty) {
        throw ValidationException(
          'Dados inválidos: ${validationErrors.join(', ')}',
        );
      }

      // Salva no Firestore
      await _microtasksCollection
          .doc(microtaskId)
          .set(microtaskWithId.toFirestore());

      // Atualiza o contador de microtasks na task pai
      await _updateTaskMicrotaskCount(microtask.taskId);

      return microtaskWithId;
    } catch (e) {
      if (e is ValidationException) rethrow;
      throw DatabaseException('Erro ao criar microtask: ${e.toString()}');
    }
  }

  /// Busca todas as tasks de um evento
  Future<List<TaskModel>> getTasks(String eventId) async {
    try {
      final querySnapshot = await _tasksCollection
          .where('eventId', isEqualTo: eventId)
          .orderBy('createdAt', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => TaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw DatabaseException('Erro ao buscar tasks: ${e.toString()}');
    }
  }

  /// Busca todas as microtasks de uma task
  Future<List<MicrotaskModel>> getMicrotasks(String taskId) async {
    try {
      final querySnapshot = await _microtasksCollection
          .where('taskId', isEqualTo: taskId)
          .orderBy('createdAt', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => MicrotaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw DatabaseException('Erro ao buscar microtasks: ${e.toString()}');
    }
  }

  /// Busca todas as microtasks de um evento
  Future<List<MicrotaskModel>> getEventMicrotasks(String eventId) async {
    try {
      final querySnapshot = await _microtasksCollection
          .where('eventId', isEqualTo: eventId)
          .orderBy('createdAt', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => MicrotaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw DatabaseException(
        'Erro ao buscar microtasks do evento: ${e.toString()}',
      );
    }
  }

  /// Busca uma task por ID
  Future<TaskModel?> getTaskById(String taskId) async {
    try {
      final doc = await _tasksCollection.doc(taskId).get();
      if (doc.exists) {
        return TaskModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw DatabaseException('Erro ao buscar task: ${e.toString()}');
    }
  }

  /// Busca uma microtask por ID
  Future<MicrotaskModel?> getMicrotaskById(String microtaskId) async {
    try {
      final doc = await _microtasksCollection.doc(microtaskId).get();
      if (doc.exists) {
        return MicrotaskModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw DatabaseException('Erro ao buscar microtask: ${e.toString()}');
    }
  }

  /// Atualiza o status de uma microtask para um usuário específico
  Future<void> updateMicrotaskStatus(
    String microtaskId,
    String userId,
    UserMicrotaskStatus status,
  ) async {
    try {
      // Busca a relação usuário-microtask
      final userMicrotaskQuery = await _userMicrotasksCollection
          .where('microtaskId', isEqualTo: microtaskId)
          .where('userId', isEqualTo: userId)
          .get();

      if (userMicrotaskQuery.docs.isEmpty) {
        throw NotFoundException('Relação usuário-microtask não encontrada');
      }

      final userMicrotaskDoc = userMicrotaskQuery.docs.first;
      final userMicrotask = UserMicrotaskModel.fromFirestore(userMicrotaskDoc);

      // Atualiza o status com timestamps apropriados
      UserMicrotaskModel updatedUserMicrotask;
      final now = DateTime.now();

      switch (status) {
        case UserMicrotaskStatus.inProgress:
          updatedUserMicrotask = userMicrotask.copyWith(
            status: status,
            startedAt: userMicrotask.startedAt ?? now,
            updatedAt: now,
          );
          break;
        case UserMicrotaskStatus.completed:
          updatedUserMicrotask = userMicrotask.copyWith(
            status: status,
            completedAt: now,
            updatedAt: now,
          );
          break;
        case UserMicrotaskStatus.cancelled:
          updatedUserMicrotask = userMicrotask.copyWith(
            status: status,
            updatedAt: now,
          );
          break;
        default:
          updatedUserMicrotask = userMicrotask.copyWith(
            status: status,
            updatedAt: now,
          );
      }

      // Salva a atualização
      await _userMicrotasksCollection
          .doc(userMicrotaskDoc.id)
          .update(updatedUserMicrotask.toFirestore());

      // Atualiza o status geral da microtask baseado nos status individuais
      await _updateMicrotaskOverallStatus(microtaskId);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException(
        'Erro ao atualizar status da microtask: ${e.toString()}',
      );
    }
  }

  /// Atualiza uma task
  Future<TaskModel> updateTask(TaskModel task) async {
    try {
      final updatedTask = task.copyWith(updatedAt: DateTime.now());

      // Valida os dados antes de salvar
      final validationErrors = updatedTask.validate();
      if (validationErrors.isNotEmpty) {
        throw ValidationException(
          'Dados inválidos: ${validationErrors.join(', ')}',
        );
      }

      await _tasksCollection.doc(task.id).update(updatedTask.toFirestore());
      return updatedTask;
    } catch (e) {
      if (e is ValidationException) rethrow;
      throw DatabaseException('Erro ao atualizar task: ${e.toString()}');
    }
  }

  /// Atualiza uma microtask
  Future<MicrotaskModel> updateMicrotask(MicrotaskModel microtask) async {
    try {
      final updatedMicrotask = microtask.copyWith(updatedAt: DateTime.now());

      // Valida os dados antes de salvar
      final validationErrors = updatedMicrotask.validate();
      if (validationErrors.isNotEmpty) {
        throw ValidationException(
          'Dados inválidos: ${validationErrors.join(', ')}',
        );
      }

      await _microtasksCollection
          .doc(microtask.id)
          .update(updatedMicrotask.toFirestore());
      return updatedMicrotask;
    } catch (e) {
      if (e is ValidationException) rethrow;
      throw DatabaseException('Erro ao atualizar microtask: ${e.toString()}');
    }
  }

  /// Deleta uma task e todas as suas microtasks
  Future<void> deleteTask(String taskId) async {
    try {
      // Busca todas as microtasks da task
      final microtasks = await getMicrotasks(taskId);

      // Deleta todas as microtasks e suas relações
      for (final microtask in microtasks) {
        await deleteMicrotask(microtask.id);
      }

      // Deleta a task
      await _tasksCollection.doc(taskId).delete();
    } catch (e) {
      throw DatabaseException('Erro ao deletar task: ${e.toString()}');
    }
  }

  /// Deleta uma microtask e todas as suas relações com usuários
  Future<void> deleteMicrotask(String microtaskId) async {
    try {
      // Busca todas as relações usuário-microtask
      final userMicrotasksQuery = await _userMicrotasksCollection
          .where('microtaskId', isEqualTo: microtaskId)
          .get();

      // Deleta todas as relações
      for (final doc in userMicrotasksQuery.docs) {
        await doc.reference.delete();
      }

      // Busca a microtask para obter o taskId
      final microtask = await getMicrotaskById(microtaskId);

      // Deleta a microtask
      await _microtasksCollection.doc(microtaskId).delete();

      // Atualiza o contador na task pai
      if (microtask != null) {
        await _updateTaskMicrotaskCount(microtask.taskId);
      }
    } catch (e) {
      throw DatabaseException('Erro ao deletar microtask: ${e.toString()}');
    }
  }

  /// Busca as relações usuário-microtask para uma microtask específica
  Future<List<UserMicrotaskModel>> getUserMicrotasks(String microtaskId) async {
    try {
      final querySnapshot = await _userMicrotasksCollection
          .where('microtaskId', isEqualTo: microtaskId)
          .get();

      return querySnapshot.docs
          .map((doc) => UserMicrotaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw DatabaseException(
        'Erro ao buscar relações usuário-microtask: ${e.toString()}',
      );
    }
  }

  /// Busca as microtasks atribuídas a um usuário específico
  Future<List<UserMicrotaskModel>> getUserAssignedMicrotasks(
    String userId,
    String eventId,
  ) async {
    try {
      final querySnapshot = await _userMicrotasksCollection
          .where('userId', isEqualTo: userId)
          .where('eventId', isEqualTo: eventId)
          .get();

      return querySnapshot.docs
          .map((doc) => UserMicrotaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw DatabaseException(
        'Erro ao buscar microtasks do usuário: ${e.toString()}',
      );
    }
  }

  /// Método auxiliar para atualizar o contador de microtasks em uma task
  Future<void> _updateTaskMicrotaskCount(String taskId) async {
    try {
      final microtasks = await getMicrotasks(taskId);
      final completedCount = microtasks.where((m) => m.isCompleted).length;

      // Determina o status da task baseado nas microtasks
      TaskStatus taskStatus;
      if (completedCount == microtasks.length && microtasks.isNotEmpty) {
        taskStatus = TaskStatus.completed;
      } else if (microtasks.any((m) => m.isInProgress || m.isAssigned)) {
        taskStatus = TaskStatus.inProgress;
      } else {
        taskStatus = TaskStatus.pending;
      }

      await _tasksCollection.doc(taskId).update({
        'microtaskCount': microtasks.length,
        'completedMicrotasks': completedCount,
        'status': taskStatus.value,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw DatabaseException(
        'Erro ao atualizar contador de microtasks: ${e.toString()}',
      );
    }
  }

  /// Método auxiliar para atualizar o status geral de uma microtask baseado nos status individuais
  Future<void> _updateMicrotaskOverallStatus(String microtaskId) async {
    try {
      final userMicrotasks = await getUserMicrotasks(microtaskId);

      if (userMicrotasks.isEmpty) {
        // Se não há usuários atribuídos, status é pending
        await _microtasksCollection.doc(microtaskId).update({
          'status': MicrotaskStatus.pending.value,
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
        return;
      }

      // Determina o status geral baseado nos status individuais
      MicrotaskStatus overallStatus;

      if (userMicrotasks.every((um) => um.isCompleted)) {
        // Todos completaram
        overallStatus = MicrotaskStatus.completed;
      } else if (userMicrotasks.any((um) => um.isInProgress)) {
        // Pelo menos um está em progresso
        overallStatus = MicrotaskStatus.inProgress;
      } else if (userMicrotasks.any((um) => um.isAssigned)) {
        // Pelo menos um está atribuído
        overallStatus = MicrotaskStatus.assigned;
      } else {
        // Todos cancelaram ou status indefinido
        overallStatus = MicrotaskStatus.cancelled;
      }

      await _microtasksCollection.doc(microtaskId).update({
        'status': overallStatus.value,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Atualiza o status da task pai
      final microtask = await getMicrotaskById(microtaskId);
      if (microtask != null) {
        await _updateTaskMicrotaskCount(microtask.taskId);
      }
    } catch (e) {
      throw DatabaseException(
        'Erro ao atualizar status geral da microtask: ${e.toString()}',
      );
    }
  }
}
