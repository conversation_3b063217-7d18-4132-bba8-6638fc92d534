import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/volunteer_profile_model.dart';
import '../../../data/models/user_microtask_model.dart';

/// Widget para exibir lista de voluntários atribuídos a uma microtask
class VolunteerListWidget extends StatelessWidget {
  final List<VolunteerProfileModel> volunteers;
  final List<UserMicrotaskModel> userMicrotasks;
  final String? currentUserId;
  final Function(String userId)? onVolunteerTap;
  final Function(String userId)? onRemoveVolunteer;
  final bool showStatus;
  final bool showActions;
  final bool isManager;

  const VolunteerListWidget({
    super.key,
    required this.volunteers,
    this.userMicrotasks = const [],
    this.currentUserId,
    this.onVolunteerTap,
    this.onRemoveVolunteer,
    this.showStatus = true,
    this.showActions = false,
    this.isManager = false,
  });

  @override
  Widget build(BuildContext context) {
    if (volunteers.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            const Text(
              'Voluntários Atribuídos',
              style: TextStyle(
                fontSize: AppDimensions.fontSizeMd,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingSm),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              ),
              child: Text(
                '${volunteers.length}',
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppDimensions.spacingMd),
        
        // Lista de voluntários
        ...volunteers.map((volunteer) => _buildVolunteerTile(volunteer)),
      ],
    );
  }

  /// Constrói um tile para um voluntário
  Widget _buildVolunteerTile(VolunteerProfileModel volunteer) {
    final userMicrotask = userMicrotasks.firstWhere(
      (um) => um.userId == volunteer.userId,
      orElse: () => UserMicrotaskModel.create(
        userId: volunteer.userId,
        microtaskId: '',
        eventId: '',
      ),
    );
    
    final isCurrentUser = currentUserId == volunteer.userId;

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingSm),
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: isCurrentUser 
            ? AppColors.primary.withValues(alpha: 0.05)
            : AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        border: Border.all(
          color: isCurrentUser 
              ? AppColors.primary.withValues(alpha: 0.3)
              : AppColors.border,
        ),
      ),
      child: InkWell(
        onTap: onVolunteerTap != null ? () => onVolunteerTap!(volunteer.userId) : null,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header do voluntário
            Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Text(
                    volunteer.displayName.isNotEmpty 
                        ? volunteer.displayName[0].toUpperCase()
                        : 'V',
                    style: const TextStyle(
                      fontSize: AppDimensions.fontSizeMd,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                
                const SizedBox(width: AppDimensions.spacingMd),
                
                // Nome e indicador de usuário atual
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            volunteer.displayName.isNotEmpty 
                                ? volunteer.displayName 
                                : 'Voluntário',
                            style: const TextStyle(
                              fontSize: AppDimensions.fontSizeMd,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          if (isCurrentUser) ...[
                            const SizedBox(width: AppDimensions.spacingSm),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppDimensions.paddingSm,
                                vertical: AppDimensions.paddingXs,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
                              ),
                              child: const Text(
                                'Você',
                                style: TextStyle(
                                  fontSize: AppDimensions.fontSizeXs,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textOnPrimary,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      if (volunteer.email.isNotEmpty) ...[
                        const SizedBox(height: AppDimensions.spacingXs),
                        Text(
                          volunteer.email,
                          style: const TextStyle(
                            fontSize: AppDimensions.fontSizeSm,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Status e ações
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (showStatus) _buildStatusChip(userMicrotask.status),
                    if (showActions && isManager) ...[
                      const SizedBox(height: AppDimensions.spacingSm),
                      _buildActionButton(volunteer.userId),
                    ],
                  ],
                ),
              ],
            ),
            
            // Habilidades do voluntário
            if (volunteer.skills.isNotEmpty) ...[
              const SizedBox(height: AppDimensions.spacingMd),
              _buildSkillsSection(volunteer.skills),
            ],
            
            // Recursos do voluntário
            if (volunteer.resources.isNotEmpty) ...[
              const SizedBox(height: AppDimensions.spacingSm),
              _buildResourcesSection(volunteer.resources),
            ],
            
            // Informações adicionais
            if (showStatus) ...[
              const SizedBox(height: AppDimensions.spacingSm),
              _buildAdditionalInfo(userMicrotask),
            ],
          ],
        ),
      ),
    );
  }

  /// Constrói chip de status
  Widget _buildStatusChip(UserMicrotaskStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;
    
    switch (status) {
      case UserMicrotaskStatus.assigned:
        backgroundColor = AppColors.info.withValues(alpha: 0.1);
        textColor = AppColors.info;
        icon = Icons.assignment_ind;
        break;
      case UserMicrotaskStatus.inProgress:
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        icon = Icons.play_circle;
        break;
      case UserMicrotaskStatus.completed:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        icon = Icons.check_circle;
        break;
      case UserMicrotaskStatus.cancelled:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        icon = Icons.cancel;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSm,
        vertical: AppDimensions.paddingXs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: textColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: textColor),
          const SizedBox(width: AppDimensions.spacingXs),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXs,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói botão de ação
  Widget _buildActionButton(String userId) {
    return IconButton(
      onPressed: onRemoveVolunteer != null ? () => onRemoveVolunteer!(userId) : null,
      icon: const Icon(Icons.remove_circle_outline),
      color: AppColors.error,
      iconSize: 20,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      padding: EdgeInsets.zero,
    );
  }

  /// Constrói seção de habilidades
  Widget _buildSkillsSection(List<String> skills) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Habilidades:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: skills.take(3).map((skill) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              ),
              child: Text(
                skill,
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  color: AppColors.primary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói seção de recursos
  Widget _buildResourcesSection(List<String> resources) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recursos:',
          style: TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: resources.take(3).map((resource) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSm,
                vertical: AppDimensions.paddingXs,
              ),
              decoration: BoxDecoration(
                color: AppColors.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              ),
              child: Text(
                resource,
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeXs,
                  color: AppColors.secondary,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Constrói informações adicionais
  Widget _buildAdditionalInfo(UserMicrotaskModel userMicrotask) {
    return Row(
      children: [
        if (userMicrotask.hoursWorked > 0) ...[
          Icon(
            Icons.schedule,
            size: 14,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: AppDimensions.spacingXs),
          Text(
            '${userMicrotask.hoursWorked}h trabalhadas',
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeXs,
              color: AppColors.textSecondary,
            ),
          ),
        ],
        const Spacer(),
        Text(
          'Atribuído ${_formatDate(userMicrotask.assignedAt)}',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// Constrói estado vazio
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLg),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        border: Border.all(color: AppColors.border),
      ),
      child: const Column(
        children: [
          Icon(
            Icons.people_outline,
            size: 48,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: AppDimensions.spacingMd),
          Text(
            'Nenhum voluntário atribuído',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingSm),
          Text(
            'Os voluntários atribuídos aparecerão aqui.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeSm,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Formata a data de forma amigável
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'há ${difference.inDays} dia${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'há ${difference.inHours} hora${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'há ${difference.inMinutes} minuto${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'agora';
    }
  }
}
