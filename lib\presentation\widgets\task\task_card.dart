import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/task_model.dart';

/// Widget de card para exibir informações de uma Task
class TaskCard extends StatelessWidget {
  final TaskModel task;
  final VoidCallback? onTap;
  final bool showProgress;
  final bool isExpanded;

  const TaskCard({
    super.key,
    required this.task,
    this.onTap,
    this.showProgress = true,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: AppDimensions.elevationSm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingMd),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header com título e status
              Row(
                children: [
                  _buildStatusIcon(),
                  const SizedBox(width: AppDimensions.spacingSm),
                  Expanded(
                    child: Text(
                      task.title,
                      style: const TextStyle(
                        fontSize: AppDimensions.fontSizeMd,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildPriorityChip(),
                ],
              ),
              
              const SizedBox(height: AppDimensions.spacingSm),
              
              // Descrição
              Text(
                task.description,
                style: const TextStyle(
                  fontSize: AppDimensions.fontSizeSm,
                  color: AppColors.textSecondary,
                ),
                maxLines: isExpanded ? null : 3,
                overflow: isExpanded ? null : TextOverflow.ellipsis,
              ),
              
              if (showProgress) ...[
                const SizedBox(height: AppDimensions.spacingMd),
                _buildProgressSection(),
              ],
              
              const SizedBox(height: AppDimensions.spacingSm),
              
              // Footer com informações adicionais
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  /// Constrói o ícone de status
  Widget _buildStatusIcon() {
    IconData icon;
    Color color;
    
    switch (task.status) {
      case TaskStatus.pending:
        icon = Icons.schedule;
        color = AppColors.warning;
        break;
      case TaskStatus.inProgress:
        icon = Icons.play_circle;
        color = AppColors.info;
        break;
      case TaskStatus.completed:
        icon = Icons.check_circle;
        color = AppColors.success;
        break;
    }
    
    return Icon(icon, color: color, size: 24);
  }

  /// Constrói o chip de prioridade
  Widget _buildPriorityChip() {
    Color backgroundColor;
    Color textColor;
    
    switch (task.priority) {
      case Priority.high:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        break;
      case Priority.medium:
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        break;
      case Priority.low:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSm,
        vertical: AppDimensions.paddingXs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: textColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        task.priority.displayName,
        style: TextStyle(
          fontSize: AppDimensions.fontSizeXs,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  /// Constrói a seção de progresso
  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progresso: ${task.completedMicrotasks}/${task.microtaskCount}',
              style: const TextStyle(
                fontSize: AppDimensions.fontSizeSm,
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              '${(task.progress * 100).toStringAsFixed(0)}%',
              style: const TextStyle(
                fontSize: AppDimensions.fontSizeSm,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        LinearProgressIndicator(
          value: task.progress,
          backgroundColor: AppColors.border,
          valueColor: AlwaysStoppedAnimation<Color>(
            task.isCompleted ? AppColors.success : AppColors.primary,
          ),
        ),
      ],
    );
  }

  /// Constrói o footer com informações adicionais
  Widget _buildFooter() {
    return Row(
      children: [
        // Status textual
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingSm,
            vertical: AppDimensions.paddingXs,
          ),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
          ),
          child: Text(
            task.status.displayName,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXs,
              fontWeight: FontWeight.w600,
              color: _getStatusColor(),
            ),
          ),
        ),
        
        const Spacer(),
        
        // Data de criação
        Text(
          'Criada ${_formatDate(task.createdAt)}',
          style: const TextStyle(
            fontSize: AppDimensions.fontSizeXs,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// Retorna a cor baseada no status
  Color _getStatusColor() {
    switch (task.status) {
      case TaskStatus.pending:
        return AppColors.warning;
      case TaskStatus.inProgress:
        return AppColors.info;
      case TaskStatus.completed:
        return AppColors.success;
    }
  }

  /// Formata a data de forma amigável
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'há ${difference.inDays} dia${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'há ${difference.inHours} hora${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'há ${difference.inMinutes} minuto${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'agora';
    }
  }
}
