import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/enums.dart';
import '../../../data/models/volunteer_profile_model.dart';
import '../../../data/models/microtask_model.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/volunteer_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/task/volunteer_assignment_card.dart';

/// Tela para gerenciar voluntários e suas atribuições
/// Disponível apenas para gerenciadores de eventos
class ManageVolunteersScreen extends StatefulWidget {
  final String eventId;

  const ManageVolunteersScreen({super.key, required this.eventId});

  @override
  State<ManageVolunteersScreen> createState() => _ManageVolunteersScreenState();
}

class _ManageVolunteersScreenState extends State<ManageVolunteersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _selectedMicrotaskId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  /// Carrega os dados iniciais
  Future<void> _loadData() async {
    final volunteerController = context.read<VolunteerController>();
    final taskController = context.read<TaskController>();

    setState(() => _isLoading = true);

    try {
      // Carrega voluntários do evento
      await volunteerController.loadEventVolunteers(widget.eventId);

      // Carrega tasks e microtasks
      await taskController.loadEventTasksHierarchy(widget.eventId);

      // Carrega atribuições de todos os voluntários
      await volunteerController.loadAllVolunteerAssignments(widget.eventId);
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Erro ao carregar dados: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const LoadingWidget(message: 'Carregando voluntários...');
    }

    return Consumer2<VolunteerController, TaskController>(
      builder: (context, volunteerController, taskController, child) {
        return Scaffold(
          backgroundColor: AppColors.background,
          body: Column(
            children: [
              // Header com tabs
              Container(
                color: AppColors.surface,
                child: TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(icon: Icon(Icons.people), text: 'Voluntários'),
                    Tab(icon: Icon(Icons.assignment), text: 'Atribuições'),
                  ],
                  labelColor: AppColors.primary,
                  unselectedLabelColor: AppColors.textSecondary,
                  indicatorColor: AppColors.primary,
                ),
              ),

              // Conteúdo das tabs
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildVolunteersTab(volunteerController, taskController),
                    _buildAssignmentsTab(volunteerController, taskController),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Constrói a tab de voluntários
  Widget _buildVolunteersTab(
    VolunteerController volunteerController,
    TaskController taskController,
  ) {
    if (volunteerController.hasError) {
      return _buildErrorState(volunteerController.errorMessage);
    }

    if (volunteerController.eventVolunteers.isEmpty) {
      return _buildEmptyVolunteersState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: Column(
        children: [
          // Header com estatísticas
          _buildVolunteersHeader(volunteerController),

          // Lista de voluntários
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(AppDimensions.paddingMd),
              itemCount: volunteerController.eventVolunteers.length,
              itemBuilder: (context, index) {
                final volunteer = volunteerController.eventVolunteers[index];
                return VolunteerAssignmentCard(
                  volunteer: volunteer,
                  assignments:
                      volunteerController.volunteerAssignments[volunteer
                          .userId] ??
                      [],
                  microtasks:
                      volunteerController.volunteerMicrotasks[volunteer
                          .userId] ??
                      [],
                  onAssignToMicrotask: (microtaskId) => _showAssignmentDialog(
                    volunteer,
                    microtaskId,
                    taskController,
                  ),
                  onRemoveFromMicrotask: (microtaskId) =>
                      _removeVolunteerFromMicrotask(
                        volunteer.userId,
                        microtaskId,
                        volunteerController,
                      ),
                  onPromoteToManager: () =>
                      _promoteVolunteer(volunteer.userId, volunteerController),
                  showActions: true,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói a tab de atribuições
  Widget _buildAssignmentsTab(
    VolunteerController volunteerController,
    TaskController taskController,
  ) {
    return Column(
      children: [
        // Seletor de microtask
        _buildMicrotaskSelector(taskController),

        // Lista de atribuições
        Expanded(
          child: _selectedMicrotaskId != null
              ? _buildMicrotaskAssignments(volunteerController, taskController)
              : _buildSelectMicrotaskPrompt(),
        ),
      ],
    );
  }

  /// Constrói header com estatísticas dos voluntários
  Widget _buildVolunteersHeader(VolunteerController volunteerController) {
    final totalVolunteers = volunteerController.eventVolunteers.length;
    final activeVolunteers = volunteerController.volunteerAssignments.values
        .where(
          (assignments) =>
              assignments.any((a) => a.isInProgress || a.isAssigned),
        )
        .length;

    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMd),
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Total',
              '$totalVolunteers',
              Icons.people,
              AppColors.primary,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingMd),
          Expanded(
            child: _buildStatCard(
              'Ativos',
              '$activeVolunteers',
              Icons.assignment_ind,
              AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói um card de estatística
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppDimensions.spacingSm),
          Text(
            value,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXl,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeSm,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói seletor de microtask
  Widget _buildMicrotaskSelector(TaskController taskController) {
    final allMicrotasks = taskController.tasksHierarchy.values
        .expand((microtasks) => microtasks)
        .toList();

    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMd),
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selecione uma Microtask',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingSm),
          DropdownButtonFormField<String>(
            value: _selectedMicrotaskId,
            hint: const Text('Escolha uma microtask para gerenciar'),
            isExpanded: true,
            items: allMicrotasks.map((microtask) {
              return DropdownMenuItem<String>(
                value: microtask.id,
                child: Text(microtask.title, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (microtaskId) {
              setState(() => _selectedMicrotaskId = microtaskId);
            },
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMd,
                vertical: AppDimensions.paddingSm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói as atribuições de uma microtask
  Widget _buildMicrotaskAssignments(
    VolunteerController volunteerController,
    TaskController taskController,
  ) {
    final microtask = taskController.tasksHierarchy.values
        .expand((microtasks) => microtasks)
        .firstWhere((m) => m.id == _selectedMicrotaskId);

    return Column(
      children: [
        // Informações da microtask
        _buildMicrotaskInfo(microtask),

        // Lista de voluntários compatíveis e atribuídos
        Expanded(
          child: DefaultTabController(
            length: 2,
            child: Column(
              children: [
                const TabBar(
                  tabs: [
                    Tab(text: 'Atribuídos'),
                    Tab(text: 'Compatíveis'),
                  ],
                  labelColor: AppColors.primary,
                  unselectedLabelColor: AppColors.textSecondary,
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildAssignedVolunteersList(
                        microtask,
                        volunteerController,
                      ),
                      _buildCompatibleVolunteersList(
                        microtask,
                        volunteerController,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Constrói informações da microtask selecionada
  Widget _buildMicrotaskInfo(MicrotaskModel microtask) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingMd),
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            microtask.title,
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingSm),
          Text(
            microtask.description,
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingMd),
          Row(
            children: [
              _buildInfoChip(
                '${microtask.assignedTo.length}/${microtask.maxVolunteers}',
                Icons.people,
                AppColors.primary,
              ),
              const SizedBox(width: AppDimensions.spacingSm),
              _buildInfoChip(
                '${microtask.estimatedHours}h',
                Icons.schedule,
                AppColors.secondary,
              ),
              const SizedBox(width: AppDimensions.spacingSm),
              _buildInfoChip(
                microtask.status.displayName,
                Icons.info,
                _getStatusColor(microtask.status),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Constrói chip de informação
  Widget _buildInfoChip(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingSm,
        vertical: AppDimensions.paddingXs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: AppDimensions.spacingXs),
          Text(
            text,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeXs,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói lista de voluntários atribuídos
  Widget _buildAssignedVolunteersList(
    MicrotaskModel microtask,
    VolunteerController volunteerController,
  ) {
    final assignedVolunteers = volunteerController.eventVolunteers
        .where((v) => microtask.assignedTo.contains(v.userId))
        .toList();

    if (assignedVolunteers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_ind,
              size: 48,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingMd),
            Text(
              'Nenhum voluntário atribuído',
              style: TextStyle(
                fontSize: AppDimensions.fontSizeMd,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      itemCount: assignedVolunteers.length,
      itemBuilder: (context, index) {
        final volunteer = assignedVolunteers[index];
        return VolunteerAssignmentCard(
          volunteer: volunteer,
          assignments:
              volunteerController.volunteerAssignments[volunteer.userId] ?? [],
          microtasks: [microtask],
          onRemoveFromMicrotask: (microtaskId) => _removeVolunteerFromMicrotask(
            volunteer.userId,
            microtaskId,
            volunteerController,
          ),
          showActions: true,
          isCompact: true,
        );
      },
    );
  }

  /// Constrói lista de voluntários compatíveis
  Widget _buildCompatibleVolunteersList(
    MicrotaskModel microtask,
    VolunteerController volunteerController,
  ) {
    final compatibleVolunteers = volunteerController.eventVolunteers
        .where((v) => !microtask.assignedTo.contains(v.userId))
        .where((v) => _isVolunteerCompatible(v, microtask))
        .toList();

    if (compatibleVolunteers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 48,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingMd),
            Text(
              'Nenhum voluntário compatível',
              style: TextStyle(
                fontSize: AppDimensions.fontSizeMd,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingMd),
      itemCount: compatibleVolunteers.length,
      itemBuilder: (context, index) {
        final volunteer = compatibleVolunteers[index];
        return VolunteerAssignmentCard(
          volunteer: volunteer,
          assignments:
              volunteerController.volunteerAssignments[volunteer.userId] ?? [],
          microtasks:
              volunteerController.volunteerMicrotasks[volunteer.userId] ?? [],
          onAssignToMicrotask: (microtaskId) => _assignVolunteerToMicrotask(
            volunteer.userId,
            microtaskId,
            volunteerController,
          ),
          showActions: true,
          isCompact: true,
        );
      },
    );
  }

  /// Verifica se um voluntário é compatível com uma microtask
  bool _isVolunteerCompatible(
    VolunteerProfileModel volunteer,
    MicrotaskModel microtask,
  ) {
    // Verifica habilidades necessárias
    if (microtask.requiredSkills.isNotEmpty) {
      final hasRequiredSkills = microtask.requiredSkills.every(
        (skill) => volunteer.skills.contains(skill),
      );
      if (!hasRequiredSkills) return false;
    }

    // Verifica recursos necessários
    if (microtask.requiredResources.isNotEmpty) {
      final hasRequiredResources = microtask.requiredResources.every(
        (resource) => volunteer.resources.contains(resource),
      );
      if (!hasRequiredResources) return false;
    }

    return true;
  }

  /// Constrói prompt para selecionar microtask
  Widget _buildSelectMicrotaskPrompt() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.task, size: 64, color: AppColors.textSecondary),
          SizedBox(height: AppDimensions.spacingMd),
          Text(
            'Selecione uma Microtask',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingSm),
          Text(
            'Escolha uma microtask acima para gerenciar suas atribuições.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Constrói estado de erro
  Widget _buildErrorState(String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppDimensions.spacingMd),
          const Text(
            'Erro ao carregar dados',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingSm),
          Text(
            errorMessage ?? 'Erro desconhecido',
            style: const TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingLg),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Tentar Novamente'),
          ),
        ],
      ),
    );
  }

  /// Constrói estado vazio de voluntários
  Widget _buildEmptyVolunteersState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64, color: AppColors.textSecondary),
          SizedBox(height: AppDimensions.spacingMd),
          Text(
            'Nenhum voluntário encontrado',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeLg,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingSm),
          Text(
            'Os voluntários do evento aparecerão aqui.',
            style: TextStyle(
              fontSize: AppDimensions.fontSizeMd,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Atribui voluntário a uma microtask
  Future<void> _assignVolunteerToMicrotask(
    String userId,
    String microtaskId,
    VolunteerController volunteerController,
  ) async {
    final success = await volunteerController.assignVolunteerToMicrotask(
      userId,
      microtaskId,
    );

    if (success && mounted) {
      _showSuccessSnackBar('Voluntário atribuído com sucesso!');
      await _loadData(); // Recarrega os dados
    } else if (mounted && volunteerController.hasError) {
      _showErrorSnackBar(
        volunteerController.errorMessage ?? 'Erro ao atribuir voluntário',
      );
    }
  }

  /// Remove voluntário de uma microtask
  Future<void> _removeVolunteerFromMicrotask(
    String userId,
    String microtaskId,
    VolunteerController volunteerController,
  ) async {
    final success = await volunteerController.removeVolunteerFromMicrotask(
      userId,
      microtaskId,
    );

    if (success && mounted) {
      _showSuccessSnackBar('Voluntário removido com sucesso!');
      await _loadData(); // Recarrega os dados
    } else if (mounted && volunteerController.hasError) {
      _showErrorSnackBar(
        volunteerController.errorMessage ?? 'Erro ao remover voluntário',
      );
    }
  }

  /// Promove voluntário a gerenciador
  Future<void> _promoteVolunteer(
    String userId,
    VolunteerController volunteerController,
  ) async {
    // TODO: Implementar promoção de voluntário
    _showErrorSnackBar('Funcionalidade em desenvolvimento');
  }

  /// Mostra diálogo de atribuição
  Future<void> _showAssignmentDialog(
    VolunteerProfileModel volunteer,
    String microtaskId,
    TaskController taskController,
  ) async {
    // TODO: Implementar diálogo de atribuição
    _showErrorSnackBar('Funcionalidade em desenvolvimento');
  }

  /// Retorna cor baseada no status da microtask
  Color _getStatusColor(MicrotaskStatus status) {
    switch (status) {
      case MicrotaskStatus.pending:
        return AppColors.warning;
      case MicrotaskStatus.assigned:
        return AppColors.info;
      case MicrotaskStatus.inProgress:
        return AppColors.primary;
      case MicrotaskStatus.completed:
        return AppColors.success;
      case MicrotaskStatus.cancelled:
        return AppColors.error;
    }
  }

  /// Mostra snackbar de sucesso
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  /// Mostra snackbar de erro
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
