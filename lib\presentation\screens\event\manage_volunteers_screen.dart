import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../controllers/volunteer_controller.dart';

/// Tela para gerenciar voluntários e suas atribuições
/// Disponível apenas para gerenciadores de eventos
class ManageVolunteersScreen extends StatefulWidget {
  final String eventId;

  const ManageVolunteersScreen({
    super.key,
    required this.eventId,
  });

  @override
  State<ManageVolunteersScreen> createState() => _ManageVolunteersScreenState();
}

class _ManageVolunteersScreenState extends State<ManageVolunteersScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<VolunteerController>(
      builder: (context, volunteerController, child) {
        return Scaffold(
          backgroundColor: AppColors.background,
          body: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 64,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: AppDimensions.spacingMd),
                Text(
                  'Gerenciar Voluntários',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSizeLg,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingSm),
                Text(
                  'Funcionalidade em desenvolvimento',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSizeMd,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
